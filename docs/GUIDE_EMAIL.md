# 📧 Guide d'utilisation du système email de l'Agent 7app

## 🎯 Vue d'ensemble

Ton agent peut maintenant :
- ✅ **Lire automatiquement** les emails non lus
- ✅ **Analyser les pièces jointes** (PDF, images, Excel, Word)
- ✅ **Générer des factures PDF** automatiquement
- ✅ **Répondre intelligemment** aux emails
- ✅ **Détecter les spams** automatiquement

## 🔧 Configuration initiale

### 1. C<PERSON>er un compte Gmail dédié (recommandé)
```
Exemple: <EMAIL>
```

### 2. Activer l'authentification à 2 facteurs
1. Aller dans **Compte Google** > **Sécurité**
2. Activer **Validation en 2 étapes**

### 3. Créer un mot de passe d'application
1. **Compte Google** > **Sécurité** > **Validation en 2 étapes**
2. **Mots de passe d'application**
3. Sélectionner **<PERSON>tre (nom personnalisé)**
4. Nommer : "Agent 7app"
5. **Copier le mot de passe de 16 caractères**

### 4. Configurer le fichier .env
```bash
# Copier .env.example vers .env
cp .env.example .env

# Éditer .env avec tes vraies valeurs
AGENT_EMAIL_ADDRESS=<EMAIL>
AGENT_EMAIL_PASSWORD=abcd-efgh-ijkl-mnop  # Mot de passe d'application
```

## 🚀 Utilisation

### 1. Traitement automatique des emails
```python
from Agent7 import executer_tache_agent

# Traiter tous les emails non lus
resultat = executer_tache_agent("Traiter automatiquement tous les emails non lus")
```

### 2. Utilisation directe du module
```python
from integrations.email_responder import lancer_traitement_emails_automatique

# Lancer le traitement
rapport = lancer_traitement_emails_automatique()
print(f"Emails traités: {rapport['emails_traites']}")
print(f"Réponses envoyées: {rapport['reponses_envoyees']}")
print(f"Factures générées: {rapport['factures_generees']}")
```

### 3. Intégration dans le pipeline principal
```python
from Agent7 import pipeline_agent

# Le traitement email est inclus automatiquement
taches = [
    "Traiter automatiquement tous les emails non lus",
    "Analyser les nouveaux documents",
    "Générer le rapport quotidien"
]

pipeline_agent(taches=taches)
```

## 🎯 Types de réponses automatiques

### 1. **Facture automatique** 
**Déclencheurs :**
- Mots-clés : "facture", "devis", "prix", "tarif"
- Pièces jointes avec montants détectés
- Documents de commande

**Action :**
- Analyse des pièces jointes
- Génération automatique d'une facture PDF
- Envoi de la facture en réponse

### 2. **Demande d'information**
**Déclencheurs :**
- Mots-clés : "information", "question", "comment"

**Action :**
- Génération d'une réponse contextuelle avec l'IA
- Réponse personnalisée et professionnelle

### 3. **Confirmation de réception**
**Déclencheurs :**
- Emails ne correspondant pas aux autres catégories

**Action :**
- Accusé de réception automatique
- Promesse de traitement manuel si nécessaire

## 📎 Analyse des pièces jointes

### Types supportés :
- **PDF** : Extraction de texte, tableaux, entités
- **Images** (JPG, PNG) : OCR avec TensorLake
- **Excel** : Lecture des feuilles et données
- **Word** : Extraction de contenu structuré
- **Texte** : Analyse directe

### Détections automatiques :
- **Factures** : Montants, TVA, dates d'échéance
- **Commandes** : Quantités, références, livraisons
- **Montants** : Extraction automatique des prix
- **Dates** : Reconnaissance de formats de dates

## 🧾 Génération de factures

### Informations extraites automatiquement :
- **Client** : Nom et email de l'expéditeur
- **Articles** : Basés sur l'analyse des pièces jointes
- **Montants** : Détectés dans les documents
- **Dates** : Génération automatique des échéances

### Personnalisation :
Éditer `data/templates/entreprise_config.json` :
```json
{
  "nom": "Mon Entreprise",
  "adresse": "123 Rue de l'Exemple\n75000 Paris",
  "telephone": "01 23 45 67 89",
  "email": "<EMAIL>",
  "siret": "123 456 789 00012",
  "tva": "FR12345678901"
}
```

## 🔒 Sécurité

### Bonnes pratiques :
- ✅ Utiliser un compte Gmail dédié
- ✅ Utiliser des mots de passe d'application (jamais le mot de passe principal)
- ✅ Garder le fichier .env secret
- ✅ Surveiller les logs pour détecter les anomalies

### Détection de spam :
- Filtrage automatique des mots-clés suspects
- Les spams sont ignorés (pas de réponse automatique)
- Logs détaillés pour audit

## 📊 Monitoring et logs

### Logs disponibles :
```python
# Voir les logs en temps réel
tail -f logs/agent.log

# Rechercher des erreurs
grep "ERROR" logs/agent.log
```

### Métriques importantes :
- Nombre d'emails traités
- Taux de réponses automatiques
- Factures générées
- Erreurs de traitement

## 🛠️ Dépannage

### Problèmes courants :

**1. Erreur d'authentification Gmail**
```
Solution: Vérifier le mot de passe d'application
```

**2. Pièces jointes non analysées**
```
Solution: Vérifier la configuration TensorLake
```

**3. Factures non générées**
```
Solution: Vérifier les permissions d'écriture dans data/factures/
```

### Tests de fonctionnement :
```python
# Test de connexion email
from integrations.email_manager import configurer_gestionnaire_email
gestionnaire = configurer_gestionnaire_email()
if gestionnaire:
    print("✅ Configuration email OK")
else:
    print("❌ Problème de configuration")
```

## 🎉 Exemples d'utilisation

### Scénario 1 : Client envoie une demande de devis
1. **Email reçu** : "Bonjour, pouvez-vous me faire un devis pour..."
2. **Action automatique** : Génération d'une facture/devis
3. **Réponse envoyée** : Email avec facture PDF en pièce jointe

### Scénario 2 : Réception d'un bon de commande
1. **Email reçu** : Avec bon de commande PDF en pièce jointe
2. **Analyse** : Extraction des articles et montants
3. **Action** : Génération de facture correspondante
4. **Réponse** : Confirmation avec facture

### Scénario 3 : Demande d'information
1. **Email reçu** : "Comment fonctionne votre service ?"
2. **Analyse IA** : Compréhension de la demande
3. **Réponse** : Explication personnalisée et professionnelle

## 📈 Évolutions futures possibles

- 🔄 Intégration avec d'autres fournisseurs email (Outlook, etc.)
- 📱 Notifications push en temps réel
- 🤖 Apprentissage automatique des préférences
- 📊 Tableaux de bord analytics
- 🔗 Intégration CRM/ERP

---

**Ton agent email est maintenant prêt à révolutionner ta gestion administrative !** 🚀

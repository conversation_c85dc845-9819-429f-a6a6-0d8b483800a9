import os
import logging
from pathlib import Path
from dotenv import load_dotenv

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Déterminer le chemin du fichier .env
env_path = Path('.env')
if not env_path.exists():
    logger.warning("⚠️ Fichier .env non trouvé dans le répertoire courant.")
    # Chercher dans le répertoire parent
    parent_env = Path('..') / '.env'
    if parent_env.exists():
        env_path = parent_env
        logger.info(f"Utilisation du fichier .env trouvé dans: {parent_env.absolute()}")

# Charger les variables d'environnement depuis le fichier .env
load_dotenv(dotenv_path=env_path)

# Clés API requises
REQUIRED_KEYS = ["OPENAI_API_KEY", "LANGCHAIN_API_KEY", "TENSORLAKE_API_KEY"]

# Clés EMAIL optionnelles
EMAIL_KEYS = ["AGENT_EMAIL_ADDRESS", "AGENT_EMAIL_PASSWORD"]

# Clés API extraites depuis les variables d'environnement
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
LANGCHAIN_API_KEY = os.getenv("LANGCHAIN_API_KEY")
TENSORLAKE_API_KEY = os.getenv("TENSORLAKE_API_KEY")

# Configuration EMAIL
AGENT_EMAIL_ADDRESS = os.getenv("AGENT_EMAIL_ADDRESS")
AGENT_EMAIL_PASSWORD = os.getenv("AGENT_EMAIL_PASSWORD")

# Points de terminaison si besoin (optionnel ici)
TENSORLAKE_ENDPOINT = os.getenv("TENSORLAKE_ENDPOINT", "https://api.tensorlake.ai")
LANGCHAIN_ENDPOINT = os.getenv("LANGCHAIN_ENDPOINT", "https://api.smith.langchain.com")

# Vérifier les clés API requises
missing_keys = [key for key in REQUIRED_KEYS if not os.getenv(key)]
if missing_keys:
    for key in missing_keys:
        logger.warning(f"⚠️ La clé API {key} n'est pas définie dans le fichier .env")
    logger.warning("Certaines fonctionnalités pourraient ne pas fonctionner correctement.")

# Vérifier les clés EMAIL optionnelles
missing_email_keys = [key for key in EMAIL_KEYS if not os.getenv(key)]
if missing_email_keys:
    logger.info("ℹ️ Configuration email non trouvée. Les fonctionnalités email seront désactivées.")
    logger.info("Pour activer les emails, ajoutez AGENT_EMAIL_ADDRESS et AGENT_EMAIL_PASSWORD dans .env")
else:
    logger.info("✅ Configuration email trouvée - Fonctionnalités email activées")

# Fonction pour vérifier la validité d'une clé API
def is_api_key_valid(key_name: str) -> bool:
    """
    Vérifie si une clé API est définie et semble valide.
    
    Args:
        key_name: Nom de la clé API à vérifier
        
    Returns:
        True si la clé est définie et semble valide, False sinon
    """
    key = os.getenv(key_name)
    if not key:
        return False
    
    # Vérifications basiques selon le type de clé
    if key_name == "OPENAI_API_KEY" and not key.startswith(("sk-", "org-")):
        return False
    if key_name == "LANGCHAIN_API_KEY" and len(key) < 10:
        return False
    if key_name == "TENSORLAKE_API_KEY" and len(key) < 10:
        return False
    
    return True

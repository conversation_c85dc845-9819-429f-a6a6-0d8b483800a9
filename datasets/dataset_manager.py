# datasets/dataset_manager.py
# Module de gestion des datasets pour l'agent

import os
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

# Configuration du logging
logger = logging.getLogger(__name__)

def charger_donnees(dataset_path: Optional[str] = None,
                   dataset_type: str = "json") -> List[Dict[str, Any]]:
    """
    Charge des données depuis un dataset.

    Args:
        dataset_path: Chemin vers le dataset (par défaut: data/datasets/)
        dataset_type: Type de dataset (json, csv)

    Returns:
        Liste des données chargées
    """
    try:
        # Chemin par défaut
        if dataset_path is None:
            dataset_path = "data/datasets"

        # Créer le répertoire s'il n'existe pas
        os.makedirs(dataset_path, exist_ok=True)

        donnees = []
        dataset_dir = Path(dataset_path)

        # Charger tous les fichiers JSON du répertoire
        if dataset_type.lower() == "json":
            for fichier_json in dataset_dir.glob("*.json"):
                try:
                    with open(fichier_json, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        if isinstance(data, list):
                            donnees.extend(data)
                        else:
                            donnees.append(data)
                    logger.info(f"✅ Dataset chargé: {fichier_json}")
                except Exception as e:
                    logger.warning(f"⚠️ Erreur lors du chargement de {fichier_json}: {e}")

        logger.info(f"📊 Total des données chargées: {len(donnees)} éléments")
        return donnees

    except Exception as e:
        logger.error(f"❌ Erreur lors du chargement des datasets: {e}")
        return []

def sauvegarder_dataset(donnees: List[Dict[str, Any]],
                       nom_fichier: str = "dataset.json",
                       dataset_path: Optional[str] = None) -> bool:
    """
    Sauvegarde des données dans un dataset.

    Args:
        donnees: Données à sauvegarder
        nom_fichier: Nom du fichier de sauvegarde
        dataset_path: Répertoire de sauvegarde

    Returns:
        True si la sauvegarde a réussi, False sinon
    """
    try:
        if dataset_path is None:
            dataset_path = "data/datasets"

        os.makedirs(dataset_path, exist_ok=True)
        fichier_complet = Path(dataset_path) / nom_fichier

        with open(fichier_complet, 'w', encoding='utf-8') as f:
            json.dump(donnees, f, indent=2, ensure_ascii=False, default=str)

        logger.info(f"✅ Dataset sauvegardé: {fichier_complet}")
        return True

    except Exception as e:
        logger.error(f"❌ Erreur lors de la sauvegarde du dataset: {e}")
        return False
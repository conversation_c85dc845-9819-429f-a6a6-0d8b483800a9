# test_email_connexion.py
# Test de connexion email complet

import os
import sys
from dotenv import load_dotenv

print("🚀 TEST DE CONNEXION EMAIL COMPLET")
print("=" * 50)

# Charger les variables d'environnement
load_dotenv()

# Test 1: Configuration de base
print("\n📋 ÉTAPE 1: Vérification de la configuration")
email = os.getenv("AGENT_EMAIL_ADDRESS")
password = os.getenv("AGENT_EMAIL_PASSWORD")

if not email or not password:
    print("❌ Configuration manquante dans .env")
    print("Vérifiez que AGENT_EMAIL_ADDRESS et AGENT_EMAIL_PASSWORD sont définis")
    sys.exit(1)

print(f"✅ Email: {email}")
print(f"✅ Mot de passe: {'*' * len(password)}")

# Test 2: Import des modules
print("\n📋 ÉTAPE 2: Import des modules")
try:
    from integrations.email_manager import GestionnaireEmail, configurer_gestionnaire_email
    print("✅ Modules email importés avec succès")
except ImportError as e:
    print(f"❌ Erreur d'import: {e}")
    sys.exit(1)

# Test 3: Création du gestionnaire
print("\n📋 ÉTAPE 3: Création du gestionnaire email")
try:
    gestionnaire = configurer_gestionnaire_email()
    if gestionnaire:
        print("✅ Gestionnaire email créé avec succès")
    else:
        print("❌ Impossible de créer le gestionnaire email")
        sys.exit(1)
except Exception as e:
    print(f"❌ Erreur création gestionnaire: {e}")
    sys.exit(1)

# Test 4: Connexion IMAP
print("\n📋 ÉTAPE 4: Test de connexion IMAP")
try:
    mail = gestionnaire.connecter_imap()
    print("✅ Connexion IMAP réussie !")
    
    # Test de sélection de boîte
    mail.select("INBOX")
    print("✅ Accès à la boîte de réception OK")
    
    # Fermer la connexion
    mail.close()
    mail.logout()
    print("✅ Déconnexion propre")
    
except Exception as e:
    print(f"❌ Erreur de connexion IMAP: {e}")
    print("\n🔧 SOLUTIONS POSSIBLES:")
    print("1. Vérifier que le mot de passe d'application est correct")
    print("2. Vérifier que la 2FA est activée sur le compte Gmail")
    print("3. Vérifier que IMAP est activé dans Gmail")
    sys.exit(1)

# Test 5: Lecture des emails
print("\n📋 ÉTAPE 5: Test de lecture des emails")
try:
    emails = gestionnaire.lire_emails_non_lus()
    print(f"✅ Lecture réussie: {len(emails)} emails non lus trouvés")
    
    if emails:
        print("\n📧 Aperçu du premier email:")
        premier_email = emails[0]
        print(f"   Expéditeur: {premier_email.get('expediteur', 'N/A')}")
        print(f"   Sujet: {premier_email.get('sujet', 'N/A')}")
        print(f"   Spam détecté: {premier_email.get('est_spam', False)}")
        print(f"   Pièces jointes: {len(premier_email.get('pieces_jointes', []))}")
    
except Exception as e:
    print(f"❌ Erreur lecture emails: {e}")

# Test 6: Test SMTP (optionnel)
print("\n📋 ÉTAPE 6: Test de capacité d'envoi SMTP")
try:
    import smtplib
    server = smtplib.SMTP("smtp.gmail.com", 587)
    server.starttls()
    server.login(email, password)
    print("✅ Connexion SMTP réussie (envoi d'emails possible)")
    server.quit()
except Exception as e:
    print(f"⚠️ Problème SMTP: {e}")
    print("   (La lecture fonctionne, mais l'envoi pourrait avoir des problèmes)")

print("\n🎉 TESTS TERMINÉS !")
print("=" * 50)
print("✅ Ton agent peut maintenant accéder aux emails Gmail !")
print("🚀 Prêt pour le traitement automatique des emails !")

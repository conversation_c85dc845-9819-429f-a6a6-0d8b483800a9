# Configuration pour l'Agent Administratif Intelligent 7app
# Copiez ce fichier vers .env et remplissez vos vraies valeurs

# ===== CLÉS API REQUISES =====
# OpenAI API Key (obligatoire)
OPENAI_API_KEY=sk-your-openai-api-key-here

# LangChain API Key (obligatoire pour le tracing)
LANGCHAIN_API_KEY=your-langchain-api-key-here

# TensorLake API Key (obligatoire pour l'analyse de documents)
TENSORLAKE_API_KEY=your-tensorlake-api-key-here

# ===== CONFIGURATION EMAIL (OPTIONNELLE) =====
# Pour activer les fonctionnalités email automatiques

# Adresse email de l'agent (Gmail recommandé)
AGENT_EMAIL_ADDRESS=<EMAIL>

# Mot de passe d'application Gmail (PAS votre mot de passe principal)
# Comment créer un mot de passe d'application Gmail:
# 1. Allez dans votre compte Google
# 2. Sécurité > Validation en 2 étapes (doit être activée)
# 3. Mots de passe d'application > Sélectionner l'app et l'appareil
# 4. Générer et copier le mot de passe de 16 caractères
AGENT_EMAIL_PASSWORD=your-gmail-app-password-here

# ===== ENDPOINTS OPTIONNELS =====
# Endpoints personnalisés (laisser par défaut si pas sûr)
TENSORLAKE_ENDPOINT=https://api.tensorlake.ai
LANGCHAIN_ENDPOINT=https://api.smith.langchain.com

# ===== CONFIGURATION AVANCÉE =====
# Répertoires de données (optionnel)
DATA_DIR=data
DOCUMENTS_DIR=data/documents
VECTORSTORE_DIR=data/vectorstore
CACHE_DIR=data/cache

# ===== NOTES IMPORTANTES =====
# 1. Ne jamais commiter le fichier .env dans Git
# 2. Utiliser des mots de passe d'application pour Gmail, pas le mot de passe principal
# 3. Garder ces clés secrètes et sécurisées
# 4. Les clés API sont sensibles à la casse

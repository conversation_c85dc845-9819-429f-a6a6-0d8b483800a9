# examples/export_documents.py
# Exemple d'utilisation de l'export de documents

import os
import sys
import logging
from pathlib import Path

# Ajouter le répertoire parent au path pour les imports
sys.path.append(str(Path(__file__).parent.parent))

from ingestion.document_ingestion import lire_documents, exporter_documents

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Exemple d'utilisation de l'export de documents."""
    try:
        # Lire les documents d'un dossier
        dossier_documents = "data/documents"
        logger.info(f"Lecture des documents du dossier: {dossier_documents}")
        documents = lire_documents(dossier_path=dossier_documents)
        
        if not documents:
            logger.warning("Aucun document trouvé.")
            return
        
        logger.info(f"{len(documents)} documents analysés.")
        
        # Exporter en CSV
        csv_path = exporter_documents(
            documents,
            format_sortie="csv",
            inclure_texte=False
        )
        logger.info(f"Documents exportés en CSV: {csv_path}")
        
        # Exporter en Excel avec texte
        excel_path = exporter_documents(
            documents,
            format_sortie="excel",
            inclure_texte=True,
            chemin_sortie="data/exports/documents_avec_texte.xlsx"
        )
        logger.info(f"Documents exportés en Excel avec texte: {excel_path}")
        
    except Exception as e:
        logger.exception(f"Erreur dans l'exemple d'export: {e}")

if __name__ == "__main__":
    main()


# Requirements pour le projet 7app - Agent Administratif Intelligent
# Généré automatiquement - Version 1.0

# ===== CORE DEPENDENCIES =====
# LangChain et OpenAI
langchain>=0.1.0
langchain-openai>=0.1.0
langchain-core>=0.1.0
langsmith>=0.1.0
openai>=1.0.0

# ===== DATA PROCESSING =====
# Manipulation de données
pandas>=2.0.0
numpy>=1.24.0

# ===== VECTOR STORE =====
# Base de données vectorielle
chromadb>=0.4.0

# ===== DOCUMENT PROCESSING =====
# Traitement de documents
python-docx>=0.8.11
openpyxl>=3.1.0
PyPDF2>=3.0.0
# Alternative PDF processing
pypdf>=3.0.0

# ===== PDF GENERATION =====
# Génération de factures PDF
reportlab>=4.0.0

# ===== WEB & API =====
# Requêtes HTTP
requests>=2.31.0
urllib3>=2.0.0

# ===== CONFIGURATION =====
# Variables d'environnement
python-dotenv>=1.0.0

# Configuration YAML
PyYAML>=6.0.0

# ===== DATA VALIDATION =====
# Validation de données
pydantic>=2.0.0

# ===== UTILITIES =====
# Utilitaires système
pathlib2>=2.3.7

# ===== OPTIONAL DEPENDENCIES =====
# Image processing (pour TensorLake)
Pillow>=10.0.0

# CSV processing (déjà inclus dans Python standard library)
# csv - built-in

# ===== DEVELOPMENT DEPENDENCIES =====
# Tests (optionnel)
pytest>=7.0.0
pytest-asyncio>=0.21.0

# Linting (optionnel)
flake8>=6.0.0
black>=23.0.0

# ===== NOTES =====
# - TensorLake: API externe, pas de package Python spécifique requis
# - SQLite3: Inclus dans Python standard library
# - JSON: Inclus dans Python standard library
# - Logging: Inclus dans Python standard library
# - OS, datetime, typing: Inclus dans Python standard library

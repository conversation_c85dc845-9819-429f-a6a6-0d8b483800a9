# integrations/email_responder.py
# Système de réponse automatique aux emails

import logging
import re
from typing import Dict, Any, List, Optional
from datetime import datetime

# Imports internes
from integrations.email_manager import GestionnaireEmail
from integrations.attachment_analyzer import analyser_pieces_jointes_email
from actions.invoice_generator import generer_facture_depuis_email

# Configuration du logging
logger = logging.getLogger(__name__)

class RepondeurEmailAutomatique:
    """Système de réponse automatique intelligent aux emails."""
    
    def __init__(self, gestionnaire_email: GestionnaireEmail):
        """
        Initialise le répondeur automatique.

        Args:
            gestionnaire_email: Instance du gestionnaire d'emails
        """
        self.gestionnaire_email = gestionnaire_email

        # Import local pour éviter l'import circulaire
        from langchain_openai import ChatOpenAI
        self.llm = ChatOpenAI(model_name="gpt-4o", temperature=0.2)

        logger.info("✅ Répondeur email automatique intelligent initialisé")

    def _analyser_intention_email(self, contenu: str, sujet: str = "", expediteur: str = "") -> Dict[str, Any]:
        """
        Analyse l'intention réelle d'un email avec une approche multi-niveaux.

        Returns:
            Dict avec intention, confiance, contexte, action_recommandee
        """
        try:
            from langchain.prompts import PromptTemplate
            import time

            # Nouveau prompt intelligent pour détecter l'intention
            prompt = PromptTemplate(
                input_variables=["sujet", "expediteur", "contenu"],
                template="""
                Tu es un expert en analyse comportementale et intentions commerciales avancées.

                CONTEXTE EMAIL:
                - SUJET: {sujet}
                - EXPÉDITEUR: {expediteur}
                - CONTENU: {contenu}

                MISSION: Analyser l'intention RÉELLE avec une précision maximale et contextuelle.

                INTENTIONS ÉTENDUES (12 types):
                1. COMMANDE_FERME - Demande explicite avec mots fermes: "facturez-moi", "je commande", "merci de facturer", "procédez à la facturation"
                2. DEMANDE_DEVIS - Demande d'estimation: "pouvez-vous me faire un devis", "estimation pour", "combien coûterait exactement"
                3. DEMANDE_TARIFS - Question prix générale: "quels sont vos tarifs", "vos prix", "grille tarifaire", "combien coûte en général"
                4. QUESTION_GENERALE - Information services: "comment ça marche", "quels services", "pouvez-vous m'expliquer"
                5. SUIVI_COMMANDE - Statut commande: "où en est ma commande", "délai de livraison", "statut de ma demande"
                6. RECLAMATION - Problème/plainte: "pas satisfait", "problème avec", "erreur dans", "réclamation"
                7. NEGOCIATION - Discussion conditions: "possible de négocier", "remise", "conditions spéciales", "votre devis est élevé"
                8. REMERCIEMENT - Satisfaction: "merci pour", "satisfait de", "excellent service", "parfait"
                9. PLANIFICATION - Rendez-vous: "quand êtes-vous disponible", "rendez-vous", "planning", "réunion"
                10. DEMANDE_SUPPORT - Assistance: "aide pour", "support technique", "assistance", "problème technique"
                11. PROPOSITION_COLLABORATION - Partenariat: "collaboration", "partenariat", "proposition commerciale"
                12. AUTRE - Non classifiable dans les catégories précédentes

                RÈGLES CRITIQUES D'ANALYSE:
                - COMMANDE_FERME: Nécessite des verbes d'action + détails précis (montants/prestations)
                - DEMANDE_TARIFS ≠ COMMANDE_FERME: "Combien coûte ?" = curiosité, pas commande
                - Analyser le CONTEXTE complet, pas juste les mots-clés isolés
                - Considérer l'URGENCE exprimée et le TON du message
                - Différencier exploration VS intention ferme

                FORMAT RÉPONSE STRUCTURÉ:
                INTENTION: [une des 12 catégories]
                CONFIANCE: [0-100]%
                CONTEXTE: [professionnel|personnel|urgent|exploratoire|commercial]
                TON: [formel|amical|neutre|impatient|poli]
                JUSTIFICATION: [analyse détaillée avec citations du texte]
                ACTION: [action spécifique recommandée]
                URGENCE: [faible|normale|haute|critique]
                SIGNAUX: [mots/phrases clés qui ont orienté l'analyse]
                """
            )

            # Retry avec backoff en cas d'erreur de connexion
            for tentative in range(3):
                try:
                    chain = prompt | self.llm
                    result = chain.invoke({
                        "sujet": sujet,
                        "expediteur": expediteur,
                        "contenu": contenu
                    })

                    # Parser la réponse structurée
                    reponse = getattr(result, "content", str(result))
                    return self._parser_analyse_intention(reponse)

                except Exception as e:
                    if "Connection error" in str(e) and tentative < 2:
                        logger.warning(f"⚠️ Tentative {tentative + 1}/3 échouée, retry dans 2s...")
                        time.sleep(2)
                        continue
                    else:
                        raise e

        except Exception as e:
            logger.error(f"❌ Erreur analyse intention: {e}")
            # Fallback intelligent sans IA
            return self._analyse_intention_fallback(contenu, sujet)

    def _parser_analyse_intention(self, reponse_ia: str) -> Dict[str, Any]:
        """Parse la réponse structurée de l'IA."""
        import re

        try:
            # Extraire les champs de la réponse
            intention_match = re.search(r'INTENTION:\s*([^\n]+)', reponse_ia)
            confiance_match = re.search(r'CONFIANCE:\s*(\d+)', reponse_ia)
            contexte_match = re.search(r'CONTEXTE:\s*([^\n]+)', reponse_ia)
            justification_match = re.search(r'JUSTIFICATION:\s*([^\n]+)', reponse_ia)
            action_match = re.search(r'ACTION:\s*([^\n]+)', reponse_ia)

            # Extraire les nouveaux champs
            ton_match = re.search(r'TON:\s*([^\n]+)', reponse_ia)
            urgence_match = re.search(r'URGENCE:\s*([^\n]+)', reponse_ia)
            signaux_match = re.search(r'SIGNAUX:\s*([^\n]+)', reponse_ia)

            # Validation de l'intention
            intention = intention_match.group(1).strip() if intention_match else 'AUTRE'
            intentions_valides = [
                'COMMANDE_FERME', 'DEMANDE_DEVIS', 'DEMANDE_TARIFS', 'QUESTION_GENERALE',
                'SUIVI_COMMANDE', 'RECLAMATION', 'NEGOCIATION', 'REMERCIEMENT',
                'PLANIFICATION', 'DEMANDE_SUPPORT', 'PROPOSITION_COLLABORATION', 'AUTRE'
            ]

            if intention not in intentions_valides:
                logger.warning(f"⚠️ Intention non valide: {intention}, fallback vers AUTRE")
                intention = 'AUTRE'

            # Validation de la confiance
            confiance = int(confiance_match.group(1)) if confiance_match else 50
            confiance = max(0, min(100, confiance))

            return {
                'intention': intention,
                'confiance': confiance,
                'contexte': contexte_match.group(1).strip() if contexte_match else 'neutre',
                'ton': ton_match.group(1).strip() if ton_match else 'neutre',
                'justification': justification_match.group(1).strip() if justification_match else 'Analyse automatique',
                'action': action_match.group(1).strip() if action_match else 'Répondre normalement',
                'urgence': urgence_match.group(1).strip() if urgence_match else 'normale',
                'signaux': signaux_match.group(1).strip() if signaux_match else 'Aucun signal spécifique',
                'reponse_brute': reponse_ia
            }
        except Exception as e:
            logger.error(f"❌ Erreur parsing intention: {e}")
            return {
                'intention': 'AUTRE',
                'confiance': 30,
                'contexte': 'neutre',
                'justification': 'Erreur de parsing',
                'action': 'Répondre prudemment',
                'reponse_brute': reponse_ia
            }

    def _analyse_intention_fallback(self, contenu: str, sujet: str = "") -> Dict[str, Any]:
        """Analyse d'intention intelligente sans IA."""
        contenu_lower = contenu.lower()
        sujet_lower = sujet.lower()
        texte_complet = f"{sujet_lower} {contenu_lower}"

        # Patterns d'intention avec contexte
        patterns_intention = {
            'COMMANDE_FERME': [
                r'(facturez|facturer|envoyer.*facture|je commande|commande ferme)',
                r'(merci de.*facture|veuillez.*facturer|procéder.*facturation)'
            ],
            'DEMANDE_DEVIS': [
                r'(devis|estimation|quote|proposer.*prix)',
                r'(faire.*devis|établir.*devis|demande.*devis)'
            ],
            'DEMANDE_TARIFS': [
                r'(quel.*tarif|combien.*coût|prix.*service|tarification)',
                r'(quels.*prix|coût.*formation|tarif.*consultation)'
            ],
            'QUESTION_GENERALE': [
                r'(comment.*marche|quels.*service|information.*sur)',
                r'(pouvez.*expliquer|j\'aimerais.*savoir|question.*sur)'
            ],
            'NEGOCIATION': [
                r'(négocier|discuter.*prix|revoir.*tarif|trop.*cher)',
                r'(budget.*serré|réduire.*coût|arrangement)'
            ],
            'RECLAMATION': [
                r'(problème|insatisfait|plainte|réclamation)',
                r'(pas.*content|déçu|erreur|dysfonctionnement)'
            ],
            'SUIVI_COMMANDE': [
                r'(où.*en.*est|statut.*commande|avancement)',
                r'(quand.*livraison|délai.*réalisation)'
            ],
            'SPAM': [
                r'(promotion|offre.*spéciale|gratuit.*maintenant)',
                r'(casino|lottery|viagra|crypto)'
            ]
        }

        # Analyser avec les patterns
        for intention, patterns in patterns_intention.items():
            for pattern in patterns:
                if re.search(pattern, texte_complet):
                    confiance = 70 if len(re.findall(pattern, texte_complet)) > 1 else 60
                    return {
                        'intention': intention,
                        'confiance': confiance,
                        'contexte': self._detecter_contexte(texte_complet),
                        'justification': f'Pattern détecté: {pattern}',
                        'action': self._mapper_intention_action(intention)
                    }

        # Aucun pattern détecté
        return {
            'intention': 'AUTRE',
            'confiance': 40,
            'contexte': self._detecter_contexte(texte_complet),
            'justification': 'Aucun pattern spécifique détecté',
            'action': 'Réponse générale'
        }

    def _detecter_contexte(self, texte: str) -> str:
        """Détecte le contexte/ton du message."""
        if any(mot in texte for mot in ['urgent', 'rapidement', 'vite', 'asap']):
            return 'urgent'
        elif any(mot in texte for mot in ['merci', 'cordialement', 'salutations']):
            return 'professionnel'
        elif any(mot in texte for mot in ['salut', 'coucou', 'hello']):
            return 'informel'
        else:
            return 'neutre'

    def _mapper_intention_action(self, intention: str) -> str:
        """Mappe une intention vers une action recommandée."""
        mapping = {
            'COMMANDE_FERME': 'Générer facture',
            'DEMANDE_DEVIS': 'Envoyer devis détaillé',
            'DEMANDE_TARIFS': 'Répondre avec grille tarifaire',
            'QUESTION_GENERALE': 'Réponse informative',
            'NEGOCIATION': 'Réponse commerciale',
            'RECLAMATION': 'Traitement prioritaire',
            'SUIVI_COMMANDE': 'Mise à jour statut',
            'REMERCIEMENT': 'Accusé de réception',
            'SPAM': 'Ignorer',
            'AUTRE': 'Réponse standard'
        }
        return mapping.get(intention, 'Réponse standard')

    def _valider_intention_avec_garde_fou(self, analyse_intention: Dict[str, Any],
                                         contenu: str, pieces_jointes: List) -> Dict[str, Any]:
        """
        Garde-fou pour valider les intentions critiques avant action.
        """
        intention = analyse_intention['intention']
        confiance = analyse_intention['confiance']

        # Garde-fou pour COMMANDE_FERME (génération de facture)
        if intention == 'COMMANDE_FERME':
            # Vérifications supplémentaires pour éviter les fausses factures (CORRIGÉ)
            mots_commande_explicite = [
                # Mots directs de facturation
                'facturez', 'facturer', 'envoyer une facture', 'je commande',
                'procéder à la facturation', 'établir une facture',

                # Expressions de confirmation de commande
                'je vous confirme la commande', 'confirme la commande',
                'commande suivante', 'commande ferme', 'bon de commande',

                # Demandes de facture
                'faire parvenir la facture', 'envoyer la facture',
                'facture dans les', 'merci de facturer', 'veuillez facturer',
                'émettre la facture', 'établir la facture',

                # Expressions contractuelles
                'merci de me faire parvenir', 'facture correspondante',
                'procéder au', 'valider la commande', 'confirmer la commande'
            ]

            if not any(mot in contenu.lower() for mot in mots_commande_explicite):
                logger.warning(f"⚠️ Garde-fou: COMMANDE_FERME détectée mais pas de mots explicites")
                # Dégrader vers DEMANDE_DEVIS
                analyse_intention['intention'] = 'DEMANDE_DEVIS'
                analyse_intention['confiance'] = max(30, confiance - 20)
                analyse_intention['justification'] += ' (dégradé par garde-fou)'

        # Garde-fou pour confiance faible
        if confiance < 50:
            logger.warning(f"⚠️ Confiance faible ({confiance}%) pour intention {intention}")
            if intention in ['COMMANDE_FERME', 'RECLAMATION']:
                # Dégrader vers une action plus sûre
                analyse_intention['intention'] = 'QUESTION_GENERALE'
                analyse_intention['justification'] += ' (dégradé - confiance faible)'

        return analyse_intention

    def _mapper_intention_vers_action(self, intention_validee: Dict[str, Any],
                                     email_info: Dict[str, Any],
                                     pieces_jointes: List) -> Dict[str, Any]:
        """
        Mappe une intention validée vers une action concrète à effectuer.
        """
        intention = intention_validee['intention']
        confiance = intention_validee['confiance']

        # Mapping intention → action avec paramètres
        actions_mapping = {
            'COMMANDE_FERME': {
                'type': 'generer_facture',
                'template': 'facture_automatique',
                'generer_pdf': True,
                'priorite': 'haute'
            },
            'DEMANDE_DEVIS': {
                'type': 'envoyer_devis',
                'template': 'devis_detaille',
                'generer_pdf': True,
                'priorite': 'normale'
            },
            'DEMANDE_TARIFS': {
                'type': 'reponse_tarifs',
                'template': 'grille_tarifaire',
                'generer_pdf': False,
                'priorite': 'normale'
            },
            'QUESTION_GENERALE': {
                'type': 'reponse_informative',
                'template': 'information_generale',
                'generer_pdf': False,
                'priorite': 'normale'
            },
            'NEGOCIATION': {
                'type': 'reponse_commerciale',
                'template': 'negociation_commerciale',
                'generer_pdf': False,
                'priorite': 'haute'
            },
            'RECLAMATION': {
                'type': 'traitement_reclamation',
                'template': 'gestion_reclamation',
                'generer_pdf': False,
                'priorite': 'urgente'
            },
            'SUIVI_COMMANDE': {
                'type': 'mise_a_jour_statut',
                'template': 'suivi_commande',
                'generer_pdf': False,
                'priorite': 'normale'
            },
            'REMERCIEMENT': {
                'type': 'accuse_reception',
                'template': 'remerciement',
                'generer_pdf': False,
                'priorite': 'basse'
            },
            'PLANIFICATION': {
                'type': 'proposer_rendez_vous',
                'template': 'planification_rdv',
                'generer_pdf': False,
                'priorite': 'normale'
            },
            'DEMANDE_SUPPORT': {
                'type': 'assistance_technique',
                'template': 'support_technique',
                'generer_pdf': False,
                'priorite': 'haute'
            },
            'PROPOSITION_COLLABORATION': {
                'type': 'evaluer_partenariat',
                'template': 'collaboration_commerciale',
                'generer_pdf': False,
                'priorite': 'normale'
            },
            'SPAM': {
                'type': 'ignorer',
                'template': None,
                'generer_pdf': False,
                'priorite': 'aucune'
            },
            'AUTRE': {
                'type': 'reponse_adaptative',
                'template': 'reponse_intelligente',
                'generer_pdf': False,
                'priorite': 'normale'
            }
        }

        action = actions_mapping.get(intention, {
            'type': 'reponse_standard',
            'template': 'confirmation_reception',
            'generer_pdf': False,
            'priorite': 'normale'
        })

        # Ajouter les métadonnées
        action.update({
            'intention_source': intention,
            'confiance': confiance,
            'email_info': email_info,
            'pieces_jointes': pieces_jointes
        })

        return action

    def _executer_action_email(self, action: Dict[str, Any], email_info: Dict[str, Any],
                              analyse_intention: Dict[str, Any], pieces_jointes: List) -> Dict[str, Any]:
        """
        Exécute l'action appropriée selon l'intention détectée.
        """
        type_action = action['type']
        expediteur_nom = email_info.get('expediteur', '').split('<')[0].strip()

        try:
            if type_action == 'generer_facture':
                return self._action_generer_facture(email_info, pieces_jointes, expediteur_nom)

            elif type_action == 'envoyer_devis':
                return self._action_envoyer_devis(email_info, pieces_jointes, expediteur_nom)

            elif type_action == 'reponse_tarifs':
                return self._action_reponse_tarifs(email_info, expediteur_nom)

            elif type_action == 'reponse_informative':
                return self._action_reponse_informative(email_info, expediteur_nom)

            elif type_action == 'reponse_commerciale':
                return self._action_reponse_commerciale(email_info, expediteur_nom)

            elif type_action == 'traitement_reclamation':
                return self._action_traitement_reclamation(email_info, expediteur_nom)

            elif type_action == 'mise_a_jour_statut':
                return self._action_mise_a_jour_statut(email_info, expediteur_nom)

            elif type_action == 'accuse_reception':
                return self._action_accuse_reception(email_info, expediteur_nom)

            elif type_action == 'proposer_rendez_vous':
                return self._action_proposer_rendez_vous(email_info, expediteur_nom)

            elif type_action == 'assistance_technique':
                return self._action_assistance_technique(email_info, expediteur_nom)

            elif type_action == 'evaluer_partenariat':
                return self._action_evaluer_partenariat(email_info, expediteur_nom)

            elif type_action == 'reponse_adaptative':
                return self._action_reponse_adaptative(email_info, expediteur_nom)

            elif type_action == 'demander_confirmation':
                return self._action_demander_confirmation(email_info, expediteur_nom)

            elif type_action == 'ignorer':
                logger.info(f"🚫 Email ignoré (spam détecté): {email_info.get('sujet', '')}")
                return {'action_effectuee': 'ignore', 'reponse_a_envoyer': None}

            else:  # reponse_standard
                return self._action_reponse_standard(email_info, expediteur_nom)

        except Exception as e:
            logger.error(f"❌ Erreur exécution action {type_action}: {e}")
            return self._action_reponse_erreur(email_info, expediteur_nom, str(e))

    def _action_generer_facture(self, email_info: Dict[str, Any], pieces_jointes: List, expediteur_nom: str) -> Dict[str, Any]:
        """Action: Générer une facture PDF (seulement pour commandes explicites)."""
        try:
            # Préparer les données pour la génération de facture
            donnees_email = {
                'expediteur': email_info.get('expediteur', ''),
                'contenu': email_info.get('contenu', ''),
                'sujet': email_info.get('sujet', '')
            }

            # Générer la facture
            fichier_facture = generer_facture_depuis_email(donnees_email, pieces_jointes)

            contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre commande. Suite à votre demande explicite, j'ai généré la facture correspondant à vos besoins.

Vous trouverez la facture en pièce jointe de ce message.

Si vous avez des questions ou souhaitez des modifications, n'hésitez pas à me contacter.

Cordialement,
Agent Administratif Intelligent"""

            return {
                'action_effectuee': 'facture_generee',
                'facture_generee': True,
                'fichier_facture': fichier_facture,
                'reponse_a_envoyer': {
                    'sujet': f"Re: {email_info.get('sujet', '')} - Facture générée",
                    'contenu': contenu_reponse,
                    'pieces_jointes': [fichier_facture] if fichier_facture else []
                }
            }
        except Exception as e:
            logger.error(f"❌ Erreur génération facture: {e}")
            return self._action_reponse_erreur(email_info, expediteur_nom, f"Erreur génération facture: {e}")

    def _action_envoyer_devis(self, email_info: Dict[str, Any], pieces_jointes: List, expediteur_nom: str) -> Dict[str, Any]:
        """Action: Envoyer un devis détaillé (sans facture finale)."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre demande de devis. Voici notre proposition détaillée :

📋 SERVICES PROPOSÉS :
• Consultation technique : 80€/heure
• Développement logiciel : 100€/heure
• Formation : 60€/heure
• Support technique : 70€/heure
• Maintenance : 65€/heure

💰 CONDITIONS :
• Tarifs HT, TVA 20% en sus
• Paiement à 30 jours
• Devis valable 30 jours

Pour confirmer votre commande, merci de me préciser les services souhaités et les quantités.

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'devis_envoye',
            'facture_generee': False,
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Devis détaillé",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_reponse_tarifs(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Répondre avec la grille tarifaire (information seulement)."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre intérêt pour nos services. Voici notre grille tarifaire :

💰 TARIFS (HT) :
• Consultation technique : 80€/heure
• Développement logiciel : 100€/heure
• Formation : 60€/heure
• Support technique : 70€/heure
• Analyse/Audit : 90€/heure
• Maintenance : 65€/heure

ℹ️ INFORMATIONS :
• TVA 20% en sus
• Forfaits possibles pour projets importants
• Tarifs dégressifs selon volume

N'hésitez pas si vous souhaitez un devis personnalisé !

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'tarifs_communiques',
            'facture_generee': False,
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Grille tarifaire",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_reponse_informative(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Réponse informative générale."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre message. Je suis un assistant administratif intelligent spécialisé dans :

🔧 NOS SERVICES :
• Consultation technique et audit
• Développement logiciel sur mesure
• Formation et accompagnement
• Support technique et maintenance

💡 COMMENT ÇA MARCHE :
• Analyse automatique de vos besoins
• Proposition de solutions adaptées
• Suivi personnalisé de vos projets

Pour une réponse plus précise, n'hésitez pas à détailler votre besoin.

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'information_fournie',
            'facture_generee': False,
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Informations sur nos services",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_reponse_commerciale(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Réponse commerciale pour négociation."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre retour. Je comprends vos préoccupations concernant nos tarifs.

💬 DISCUSSION COMMERCIALE :
• Nous sommes ouverts à la discussion selon votre projet
• Possibilité de forfaits pour volumes importants
• Tarifs dégressifs selon la durée d'engagement
• Solutions adaptées à votre budget

📞 PROCHAINES ÉTAPES :
Pouvons-nous organiser un échange pour mieux comprendre vos besoins et contraintes ?

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'reponse_commerciale',
            'facture_generee': False,
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Discussion commerciale",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_traitement_reclamation(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Traitement prioritaire des réclamations."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Je prends note de votre réclamation et la traite en priorité.

🚨 TRAITEMENT PRIORITAIRE :
• Votre message a été escaladé
• Prise en charge immédiate
• Suivi personnalisé garanti

📋 PROCHAINES ÉTAPES :
• Analyse détaillée de votre situation
• Proposition de solution sous 24h
• Contact direct pour résolution

Nous nous excusons pour tout désagrément et mettons tout en œuvre pour résoudre rapidement votre problème.

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'reclamation_traitee',
            'facture_generee': False,
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Réclamation prise en charge",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_mise_a_jour_statut(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Mise à jour du statut de commande."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre demande de suivi.

📊 STATUT DE VOTRE COMMANDE :
• Commande reçue et en cours de traitement
• Délai estimé : selon complexité du projet
• Prochaine mise à jour : sous 48h

📞 CONTACT :
Pour un suivi détaillé, n'hésitez pas à me contacter directement.

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'statut_communique',
            'facture_generee': False,
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Statut de votre commande",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_accuse_reception(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Simple accusé de réception."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre message. J'ai bien reçu votre retour.

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'accuse_reception',
            'facture_generee': False,
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Accusé de réception",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_reponse_standard(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Réponse standard de sécurité."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre message. J'ai bien reçu votre email et je l'analyse.

Je reviendrai vers vous prochainement avec une réponse adaptée à votre demande.

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'reponse_standard',
            'facture_generee': False,
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Message reçu",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_reponse_erreur(self, email_info: Dict[str, Any], expediteur_nom: str, erreur: str) -> Dict[str, Any]:
        """Action: Réponse en cas d'erreur de traitement."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre message. J'ai rencontré une difficulté technique lors du traitement automatique de votre email.

Votre message a été transmis pour traitement manuel et vous recevrez une réponse personnalisée prochainement.

Nous nous excusons pour ce désagrément.

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'erreur_traitee',
            'facture_generee': False,
            'erreur': erreur,
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Traitement en cours",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def traiter_emails_non_lus(self) -> Dict[str, Any]:
        """
        Traite tous les emails non lus et génère des réponses automatiques.
        
        Returns:
            Rapport de traitement
        """
        try:
            # Lire les emails non lus
            emails = self.gestionnaire_email.lire_emails_non_lus()
            
            rapport = {
                "emails_traites": 0,
                "reponses_envoyees": 0,
                "factures_generees": 0,
                "erreurs": [],
                "details": []
            }
            
            for email_info in emails:
                try:
                    # Ignorer les spams
                    if email_info.get('est_spam', False):
                        logger.info(f"🚫 Email spam ignoré de {email_info.get('expediteur', 'inconnu')}")
                        continue
                    
                    # Traiter l'email
                    resultat = self.traiter_email_individuel(email_info)
                    
                    rapport["emails_traites"] += 1
                    rapport["details"].append(resultat)
                    
                    if resultat.get("reponse_envoyee"):
                        rapport["reponses_envoyees"] += 1
                    
                    if resultat.get("facture_generee"):
                        rapport["factures_generees"] += 1
                    
                    # Marquer comme lu
                    self.gestionnaire_email.marquer_comme_lu(email_info["id"])
                    
                except Exception as e:
                    erreur = f"Erreur traitement email {email_info.get('id', 'inconnu')}: {e}"
                    logger.error(f"❌ {erreur}")
                    rapport["erreurs"].append(erreur)
            
            logger.info(f"📊 Rapport: {rapport['emails_traites']} emails traités, {rapport['reponses_envoyees']} réponses envoyées")
            return rapport
            
        except Exception as e:
            logger.error(f"❌ Erreur traitement emails: {e}")
            return {"erreur": str(e)}
    
    def traiter_email_individuel(self, email_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        NOUVELLE LOGIQUE: Traite un email avec analyse d'intention intelligente.

        Args:
            email_info: Informations de l'email

        Returns:
            Résultat du traitement avec intention détectée
        """
        try:
            expediteur = email_info.get('expediteur', '')
            sujet = email_info.get('sujet', '')
            contenu = email_info.get('contenu', '')
            pieces_jointes = email_info.get('pieces_jointes', [])

            logger.info(f"📧 Traitement intelligent email de {expediteur}: {sujet}")

            # ÉTAPE 1: Analyse d'intention avec IA ou fallback
            analyse_intention = self._analyser_intention_email(contenu, sujet, expediteur)
            logger.info(f"🧠 Intention détectée: {analyse_intention['intention']} (confiance: {analyse_intention['confiance']}%)")

            # ÉTAPE 2: Analyser les pièces jointes si présentes
            analyse_pieces_jointes = []
            if pieces_jointes:
                analyse_pieces_jointes = analyser_pieces_jointes_email(pieces_jointes)
                logger.info(f"📎 {len(pieces_jointes)} pièces jointes analysées")

            # ÉTAPE 3: Garde-fou de validation pour les actions critiques
            intention_validee = self._valider_intention_avec_garde_fou(
                analyse_intention, contenu, pieces_jointes
            )

            # ÉTAPE 4: Mapper intention → action concrète
            action_a_effectuer = self._mapper_intention_vers_action(
                intention_validee, email_info, analyse_pieces_jointes
            )

            # ÉTAPE 5: Exécuter l'action appropriée
            resultat_action = self._executer_action_email(
                action_a_effectuer, email_info, analyse_intention, analyse_pieces_jointes
            )

            # ÉTAPE 6: Envoyer la réponse si nécessaire
            reponse_envoyee = False
            if resultat_action.get('reponse_a_envoyer'):
                reponse_info = resultat_action['reponse_a_envoyer']
                reponse_envoyee = self.gestionnaire_email.envoyer_email(
                    destinataire=expediteur,
                    sujet=reponse_info['sujet'],
                    contenu=reponse_info['contenu'],
                    pieces_jointes=reponse_info.get('pieces_jointes', []),
                    en_reponse_a=email_info['id']
                )
                logger.info(f"📤 Réponse envoyée: {reponse_envoyee}")

            return {
                "email_id": email_info['id'],
                "expediteur": expediteur,
                "sujet": sujet,
                "intention_detectee": analyse_intention['intention'],
                "confiance_intention": analyse_intention['confiance'],
                "contexte": analyse_intention['contexte'],
                "action_effectuee": action_a_effectuer['type'],
                "reponse_envoyee": reponse_envoyee,
                "facture_generee": resultat_action.get('facture_generee', False),
                "analyse_complete": {
                    "intention": analyse_intention,
                    "pieces_jointes": analyse_pieces_jointes,
                    "action": action_a_effectuer,
                    "resultat": resultat_action
                }
            }

        except Exception as e:
            logger.error(f"❌ Erreur traitement email individuel: {e}")
            return {"erreur": str(e), "email_id": email_info.get('id', 'inconnu')}

    # ============================================================================
    # NOUVELLES ACTIONS PERSONNALISÉES ÉTENDUES
    # ============================================================================

    def _action_proposer_rendez_vous(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Proposer des créneaux de rendez-vous."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre demande de rendez-vous.

Je vous propose les créneaux suivants :
• Lundi 14h-16h
• Mardi 10h-12h
• Mercredi 15h-17h
• Jeudi 9h-11h

Merci de me confirmer le créneau qui vous convient le mieux.

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'proposition_rdv',
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Proposition de créneaux",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_assistance_technique(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Fournir une assistance technique spécialisée."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre demande d'assistance technique.

Notre équipe support va analyser votre demande et vous contacter dans les plus brefs délais avec une solution adaptée.

En attendant, vous pouvez consulter notre base de connaissances ou nous fournir plus de détails sur votre problème.

Cordialement,
Support Technique Intelligent"""

        return {
            'action_effectuee': 'support_technique',
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Support technique",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_evaluer_partenariat(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Évaluer une proposition de partenariat."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre proposition de collaboration.

Votre proposition nous intéresse et nous souhaitons l'étudier en détail. Notre équipe commerciale va analyser votre demande et reviendra vers vous avec une réponse détaillée.

Nous vous recontacterons sous 48h pour discuter des modalités de collaboration.

Cordialement,
Équipe Commerciale"""

        return {
            'action_effectuee': 'evaluation_partenariat',
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Proposition de partenariat",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }

    def _action_reponse_adaptative(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Réponse intelligente adaptée au contexte avec IA."""
        try:
            # Générer une réponse contextuelle avec l'IA
            prompt = f"""
            Génère une réponse professionnelle et adaptée à cet email :

            Expéditeur: {expediteur_nom}
            Sujet: {email_info.get('sujet', '')}
            Contenu: {email_info.get('contenu', '')}

            La réponse doit être :
            - Professionnelle et courtoise
            - Adaptée au contexte spécifique
            - Utile et informative
            - En français
            - Maximum 200 mots
            """

            response = self.llm.invoke(prompt)
            contenu_ia = getattr(response, "content", str(response))

            return {
                'action_effectuee': 'reponse_adaptative',
                'reponse_a_envoyer': {
                    'sujet': f"Re: {email_info.get('sujet', '')}",
                    'contenu': contenu_ia,
                    'pieces_jointes': []
                }
            }

        except Exception as e:
            logger.error(f"❌ Erreur réponse adaptative: {e}")
            return self._action_reponse_standard(email_info, expediteur_nom)

    def _action_demander_confirmation(self, email_info: Dict[str, Any], expediteur_nom: str) -> Dict[str, Any]:
        """Action: Demander confirmation avant facturation."""
        contenu_reponse = f"""Bonjour {expediteur_nom},

Merci pour votre message. J'ai analysé votre demande et je souhaite confirmer les détails avant de procéder à la facturation :

Prestations identifiées :
{email_info.get('contenu', '')[:200]}...

Pouvez-vous confirmer que vous souhaitez que je procède à la facturation de ces prestations ?

Merci de me confirmer pour que je puisse générer votre facture.

Cordialement,
Agent Administratif Intelligent"""

        return {
            'action_effectuee': 'demande_confirmation',
            'reponse_a_envoyer': {
                'sujet': f"Re: {email_info.get('sujet', '')} - Confirmation avant facturation",
                'contenu': contenu_reponse,
                'pieces_jointes': []
            }
        }


# ============================================================================
# FONCTION PRINCIPALE - NOUVELLE LOGIQUE INTELLIGENTE
# ============================================================================

def lancer_traitement_emails_automatique() -> Dict[str, Any]:
    """
    Lance le traitement automatique des emails avec la nouvelle logique intelligente.

    Returns:
        Rapport détaillé du traitement
    """
    try:
        from integrations.email_manager import GestionnaireEmail
        from config import AGENT_EMAIL_ADDRESS, AGENT_EMAIL_PASSWORD

        # Initialiser le gestionnaire d'emails
        gestionnaire_email = GestionnaireEmail(AGENT_EMAIL_ADDRESS, AGENT_EMAIL_PASSWORD)

        # Initialiser le répondeur intelligent
        repondeur = RepondeurEmailAutomatique(gestionnaire_email)

        # Traiter les emails avec la nouvelle logique
        rapport = repondeur.traiter_emails_non_lus()

        return rapport

    except Exception as e:
        logger.error(f"❌ Erreur traitement automatique: {e}")
        return {
            "emails_traites": 0,
            "reponses_envoyees": 0,
            "factures_generees": 0,
            "erreurs": [str(e)],
            "details": []
        }

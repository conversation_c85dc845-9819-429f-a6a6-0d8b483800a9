# integrations/attachment_analyzer.py
# Analyseur de pièces jointes pour emails

import os
import tempfile
import logging
from typing import Dict, Any, List, Optional
from pathlib import Path
import base64
import mimetypes

# Imports pour l'analyse de documents
from integrations.tensorlake_tools import analyser_document_avec_tensorlake
import PyPDF2
from PIL import Image
import pandas as pd

# Configuration du logging
logger = logging.getLogger(__name__)

class AnalyseurPiecesJointes:
    """Analyseur intelligent de pièces jointes d'emails."""
    
    def __init__(self):
        """Initialise l'analyseur."""
        self.types_supportes = {
            'application/pdf': self._analyser_pdf,
            'image/jpeg': self._analyser_image,
            'image/png': self._analyser_image,
            'image/jpg': self._analyser_image,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': self._analyser_excel,
            'application/vnd.ms-excel': self._analyser_excel,
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': self._analyser_word,
            'text/plain': self._analyser_texte
        }
        logger.info("✅ Analyseur de pièces jointes initialisé")
    
    def analyser_pieces_jointes(self, pieces_jointes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Analyse toutes les pièces jointes d'un email.
        
        Args:
            pieces_jointes: Liste des pièces jointes de l'email
            
        Returns:
            Liste des analyses de chaque pièce jointe
        """
        analyses = []
        
        for piece_jointe in pieces_jointes:
            try:
                analyse = self.analyser_piece_jointe(piece_jointe)
                analyses.append(analyse)
            except Exception as e:
                logger.error(f"❌ Erreur analyse pièce jointe {piece_jointe.get('nom', 'inconnue')}: {e}")
                analyses.append({
                    "nom": piece_jointe.get('nom', 'inconnue'),
                    "erreur": str(e),
                    "analysable": False
                })
        
        logger.info(f"📎 {len(analyses)} pièces jointes analysées")
        return analyses
    
    def analyser_piece_jointe(self, piece_jointe: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyse une pièce jointe spécifique.
        
        Args:
            piece_jointe: Informations sur la pièce jointe
            
        Returns:
            Analyse détaillée de la pièce jointe
        """
        nom = piece_jointe.get('nom', 'inconnu')
        type_mime = piece_jointe.get('type', '')
        contenu = piece_jointe.get('contenu', b'')
        
        logger.info(f"🔍 Analyse de {nom} (type: {type_mime})")
        
        # Résultat de base
        analyse = {
            "nom": nom,
            "type": type_mime,
            "taille": len(contenu) if contenu else 0,
            "analysable": False,
            "contenu_extrait": "",
            "entites": {},
            "tableaux": [],
            "est_facture": False,
            "est_commande": False,
            "montants_detectes": [],
            "dates_detectees": []
        }
        
        # Vérifier si le type est supporté
        if type_mime in self.types_supportes:
            try:
                # Sauvegarder temporairement le fichier
                with tempfile.NamedTemporaryFile(delete=False, suffix=self._get_extension(nom)) as temp_file:
                    if isinstance(contenu, str):
                        # Décoder base64 si nécessaire
                        contenu = base64.b64decode(contenu)
                    temp_file.write(contenu)
                    temp_path = temp_file.name
                
                # Analyser selon le type
                analyse_specifique = self.types_supportes[type_mime](temp_path)
                analyse.update(analyse_specifique)
                analyse["analysable"] = True
                
                # Nettoyage
                os.unlink(temp_path)
                
            except Exception as e:
                logger.error(f"❌ Erreur analyse spécifique {nom}: {e}")
                analyse["erreur"] = str(e)
        else:
            logger.warning(f"⚠️ Type {type_mime} non supporté pour {nom}")
        
        # Analyse sémantique générale
        self._analyser_semantique(analyse)
        
        return analyse
    
    def _analyser_pdf(self, fichier_path: str) -> Dict[str, Any]:
        """Analyse un fichier PDF."""
        try:
            # Utiliser TensorLake pour l'analyse avancée
            analyse_tensorlake = analyser_document_avec_tensorlake(fichier_path)
            
            # Analyse basique avec PyPDF2 en fallback
            texte_fallback = ""
            try:
                with open(fichier_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        texte_fallback += page.extract_text()
            except:
                pass
            
            return {
                "contenu_extrait": analyse_tensorlake.get("texte", texte_fallback),
                "tableaux": analyse_tensorlake.get("tableaux", []),
                "entites": analyse_tensorlake.get("entites", {}),
                "nb_pages": len(PyPDF2.PdfReader(open(fichier_path, 'rb')).pages) if os.path.exists(fichier_path) else 0
            }
        except Exception as e:
            logger.error(f"❌ Erreur analyse PDF: {e}")
            return {"erreur": str(e)}
    
    def _analyser_image(self, fichier_path: str) -> Dict[str, Any]:
        """Analyse une image."""
        try:
            # Utiliser TensorLake pour OCR
            analyse_tensorlake = analyser_document_avec_tensorlake(fichier_path)
            
            # Informations de base de l'image
            with Image.open(fichier_path) as img:
                largeur, hauteur = img.size
                format_img = img.format
            
            return {
                "contenu_extrait": analyse_tensorlake.get("texte", ""),
                "entites": analyse_tensorlake.get("entites", {}),
                "largeur": largeur,
                "hauteur": hauteur,
                "format": format_img
            }
        except Exception as e:
            logger.error(f"❌ Erreur analyse image: {e}")
            return {"erreur": str(e)}
    
    def _analyser_excel(self, fichier_path: str) -> Dict[str, Any]:
        """Analyse un fichier Excel."""
        try:
            # Lire le fichier Excel
            df = pd.read_excel(fichier_path, sheet_name=None)  # Toutes les feuilles
            
            contenu = ""
            tableaux = []
            
            for nom_feuille, donnees in df.items():
                contenu += f"\n=== Feuille: {nom_feuille} ===\n"
                contenu += donnees.to_string()
                
                # Convertir en format tableau
                tableaux.append({
                    "nom_feuille": nom_feuille,
                    "colonnes": list(donnees.columns),
                    "nb_lignes": len(donnees),
                    "donnees": donnees.to_dict('records')[:10]  # Limiter à 10 lignes
                })
            
            return {
                "contenu_extrait": contenu,
                "tableaux": tableaux,
                "nb_feuilles": len(df)
            }
        except Exception as e:
            logger.error(f"❌ Erreur analyse Excel: {e}")
            return {"erreur": str(e)}
    
    def _analyser_word(self, fichier_path: str) -> Dict[str, Any]:
        """Analyse un fichier Word."""
        try:
            # Utiliser TensorLake
            analyse_tensorlake = analyser_document_avec_tensorlake(fichier_path)
            
            return {
                "contenu_extrait": analyse_tensorlake.get("texte", ""),
                "tableaux": analyse_tensorlake.get("tableaux", []),
                "entites": analyse_tensorlake.get("entites", {})
            }
        except Exception as e:
            logger.error(f"❌ Erreur analyse Word: {e}")
            return {"erreur": str(e)}
    
    def _analyser_texte(self, fichier_path: str) -> Dict[str, Any]:
        """Analyse un fichier texte."""
        try:
            with open(fichier_path, 'r', encoding='utf-8') as file:
                contenu = file.read()
            
            return {
                "contenu_extrait": contenu,
                "nb_lignes": len(contenu.split('\n')),
                "nb_caracteres": len(contenu)
            }
        except Exception as e:
            logger.error(f"❌ Erreur analyse texte: {e}")
            return {"erreur": str(e)}
    
    def _analyser_semantique(self, analyse: Dict[str, Any]) -> None:
        """Analyse sémantique du contenu pour détecter le type de document."""
        contenu = analyse.get("contenu_extrait", "").lower()
        
        # Détection de facture
        mots_facture = ["facture", "invoice", "montant", "total", "tva", "ht", "ttc", "€", "$"]
        if any(mot in contenu for mot in mots_facture):
            analyse["est_facture"] = True
        
        # Détection de commande
        mots_commande = ["commande", "order", "quantité", "référence", "livraison", "bon de commande"]
        if any(mot in contenu for mot in mots_commande):
            analyse["est_commande"] = True
        
        # Extraction de montants (regex simple)
        import re
        montants = re.findall(r'(\d+[,.]?\d*)\s*[€$]', contenu)
        analyse["montants_detectes"] = [float(m.replace(',', '.')) for m in montants]
        
        # Extraction de dates
        dates = re.findall(r'\d{1,2}[/-]\d{1,2}[/-]\d{2,4}', contenu)
        analyse["dates_detectees"] = dates
    
    def _get_extension(self, nom_fichier: str) -> str:
        """Récupère l'extension d'un fichier."""
        return Path(nom_fichier).suffix or '.tmp'

def analyser_pieces_jointes_email(pieces_jointes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    Fonction utilitaire pour analyser les pièces jointes d'un email.
    
    Args:
        pieces_jointes: Liste des pièces jointes
        
    Returns:
        Liste des analyses
    """
    analyseur = AnalyseurPiecesJointes()
    return analyseur.analyser_pieces_jointes(pieces_jointes)

# integrations/tensorlake_tools.py
# Outils d'intégration avec TensorLake pour l'analyse de documents

import os
import logging
from typing import Dict, Any, Optional, Union, List
import json
import requests
from pathlib import Path
import mimetypes
import base64

# Configuration du logging
logger = logging.getLogger(__name__)

# Récupérer la clé API depuis les variables d'environnement
TENSORLAKE_API_KEY = os.environ.get("TENSORLAKE_API_KEY", "")

class TensorLakeClient:
    """Client pour l'API TensorLake."""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialise le client TensorLake.
        
        Args:
            api_key: Clé API TensorLake (par défaut: variable d'environnement TENSORLAKE_API_KEY)
        """
        self.api_key = api_key or TENSORLAKE_API_KEY
        if not self.api_key:
            logger.warning("⚠️ Aucune clé API TensorLake trouvée. Certaines fonctionnalités seront limitées.")
        
        self.base_url = "https://api.tensorlake.ai"
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
    
    def analyser_document(self, fichier_path: Union[str, Path]) -> Dict[str, Any]:
        """
        Analyse un document avec TensorLake.
        
        Args:
            fichier_path: Chemin vers le fichier à analyser
            
        Returns:
            Résultats de l'analyse
        """
        try:
            fichier_path = Path(fichier_path)
            
            if not fichier_path.exists():
                raise FileNotFoundError(f"Le fichier {fichier_path} n'existe pas")
            
            # Déterminer le type MIME
            mime_type, _ = mimetypes.guess_type(fichier_path)
            if not mime_type:
                mime_type = "application/octet-stream"
            
            # NOUVELLE API TensorLake v1 (2024)
            logger.info(f"🔄 Upload du fichier vers TensorLake: {fichier_path.name}")

            # ÉTAPE 1: Upload du fichier
            file_id = self._upload_fichier_tensorlake_v1(fichier_path, mime_type)
            if not file_id:
                return {"error": "Échec upload fichier"}

            logger.info(f"✅ Fichier uploadé avec ID: {file_id}")

            # ÉTAPE 2: Analyse du fichier
            return self._analyser_fichier_tensorlake_v1(file_id)
            
        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Erreur lors de la requête à TensorLake: {e}")
            if hasattr(e, "response") and e.response is not None:
                logger.error(f"Détails: {e.response.text}")
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'analyse du document: {e}")
            return {"error": str(e)}

    def _upload_fichier_tensorlake_v1(self, fichier_path: Path, mime_type: str) -> Optional[str]:
        """
        Upload un fichier vers TensorLake avec la nouvelle API v1.

        Args:
            fichier_path: Chemin vers le fichier
            mime_type: Type MIME du fichier

        Returns:
            file_id si succès, None sinon
        """
        try:
            # Préparer le fichier pour upload multipart
            with open(fichier_path, "rb") as f:
                files = {
                    'file': (fichier_path.name, f, mime_type)
                }

                # Headers pour upload (sans Content-Type pour multipart)
                headers = {
                    'Authorization': f'Bearer {self.api_key}'
                }

                # Upload vers le nouveau endpoint v1
                response = requests.post(
                    f"{self.base_url}/documents/v1/files",
                    headers=headers,
                    files=files,
                    timeout=60
                )

            response.raise_for_status()
            result = response.json()

            # Extraire le file_id de la réponse
            file_id = result.get('file_id') or result.get('id') or result.get('document_id')

            if not file_id:
                logger.error(f"❌ Pas de file_id dans la réponse: {result}")
                return None

            return file_id

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Erreur upload TensorLake v1: {e}")
            if hasattr(e, "response") and e.response is not None:
                logger.error(f"Détails: {e.response.text}")
            return None
        except Exception as e:
            logger.error(f"❌ Erreur upload fichier: {e}")
            return None

    def _analyser_fichier_tensorlake_v1(self, file_id: str) -> Dict[str, Any]:
        """
        Analyse un fichier uploadé avec TensorLake API v1.

        Args:
            file_id: ID du fichier uploadé

        Returns:
            Résultats de l'analyse
        """
        try:
            # Payload corrigé selon l'erreur "Either file or content is required"
            payload = {
                "file": file_id,  # Utiliser "file" au lieu de "file_id"
                "extract_text": True,
                "extract_tables": True,
                "extract_entities": True
            }

            # Analyse avec le nouveau endpoint v1
            response = requests.post(
                f"{self.base_url}/documents/v1/parse",
                headers=self.headers,
                json=payload,
                timeout=120  # Analyse peut prendre du temps
            )

            response.raise_for_status()
            result = response.json()

            logger.info(f"✅ Analyse TensorLake v1 réussie pour file_id: {file_id}")

            # Normaliser la réponse pour compatibilité
            return {
                "text": result.get("text", ""),
                "tables": result.get("tables", []),
                "entities": result.get("entities", {}),
                "metadata": result.get("metadata", {}),
                "file_id": file_id,
                "success": True
            }

        except requests.exceptions.RequestException as e:
            logger.error(f"❌ Erreur analyse TensorLake v1: {e}")
            if hasattr(e, "response") and e.response is not None:
                logger.error(f"Détails: {e.response.text}")
            return {"error": str(e), "file_id": file_id}
        except Exception as e:
            logger.error(f"❌ Erreur analyse fichier: {e}")
            return {"error": str(e), "file_id": file_id}

    def extraire_texte(self, fichier_path: Union[str, Path]) -> str:
        """
        Extrait le texte d'un document avec TensorLake.
        
        Args:
            fichier_path: Chemin vers le fichier à analyser
            
        Returns:
            Texte extrait du document
        """
        try:
            resultats = self.analyser_document(fichier_path)
            return resultats.get("text", "")
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'extraction du texte: {e}")
            return ""
    
    def extraire_tableaux(self, fichier_path: Union[str, Path]) -> List[Dict[str, Any]]:
        """
        Extrait les tableaux d'un document avec TensorLake.
        
        Args:
            fichier_path: Chemin vers le fichier à analyser
            
        Returns:
            Liste des tableaux extraits
        """
        try:
            resultats = self.analyser_document(fichier_path)
            return resultats.get("tables", [])
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'extraction des tableaux: {e}")
            return []
    
    def extraire_entites(self, fichier_path: Union[str, Path]) -> Dict[str, List[Dict[str, Any]]]:
        """
        Extrait les entités nommées d'un document avec TensorLake.
        
        Args:
            fichier_path: Chemin vers le fichier à analyser
            
        Returns:
            Dictionnaire des entités par type
        """
        try:
            resultats = self.analyser_document(fichier_path)
            return resultats.get("entities", {})
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'extraction des entités: {e}")
            return {}

# Fonction d'analyse de document avec TensorLake
def analyser_document_avec_tensorlake(fichier_path: Union[str, Path]) -> Dict[str, Any]:
    """
    Analyse un document avec TensorLake et retourne les résultats formatés.
    
    Args:
        fichier_path: Chemin vers le fichier à analyser
        
    Returns:
        Résultats de l'analyse formatés
    """
    try:
        # Initialiser le client TensorLake
        client = TensorLakeClient()
        
        # Analyser le document
        resultats_bruts = client.analyser_document(fichier_path)
        
        # Vérifier s'il y a une erreur
        if "error" in resultats_bruts:
            logger.error(f"❌ Erreur TensorLake: {resultats_bruts['error']}")
            return {
                "texte": "",
                "tableaux": [],
                "entites": {},
                "error": resultats_bruts["error"]
            }
        
        # Formater les résultats
        resultats_formattés = {
            "texte": resultats_bruts.get("text", ""),
            "tableaux": resultats_bruts.get("tables", []),
            "entites": resultats_bruts.get("entities", {}),
            "layout": resultats_bruts.get("layout", {})
        }
        
        return resultats_formattés
        
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'analyse du document: {e}")
        return {
            "texte": "",
            "tableaux": [],
            "entites": {},
            "error": str(e)
        }

# Alias pour compatibilité
TensorLakeAnalyzer = TensorLakeClient

def analyser_email_avec_tensorlake_ou_fallback(contenu_email: str) -> Dict[str, Any]:
    """
    Analyse un email avec TensorLake ou fallback IA si indisponible.

    Args:
        contenu_email: Contenu de l'email à analyser

    Returns:
        Analyse structurée avec entités extraites
    """
    try:
        # Tentative avec TensorLake
        client = TensorLakeClient()

        # Créer un fichier temporaire pour l'email
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(contenu_email)
            temp_file = f.name

        try:
            # Analyser avec TensorLake
            from pathlib import Path
            resultat = client.analyser_document(Path(temp_file))

            if resultat and not resultat.get('error'):
                logger.info("✅ Analyse TensorLake réussie")
                return {
                    'source': 'tensorlake',
                    'texte': resultat.get('text', contenu_email),
                    'entites': resultat.get('entities', {}),
                    'tableaux': resultat.get('tables', []),
                    'success': True
                }
        finally:
            # Nettoyer le fichier temporaire
            os.unlink(temp_file)

    except Exception as e:
        logger.warning(f"⚠️ TensorLake indisponible: {e}")

    # Fallback vers analyse IA
    logger.info("🔄 Fallback vers analyse IA")
    return _analyser_email_avec_ia_fallback(contenu_email)

def _analyser_email_avec_ia_fallback(contenu_email: str) -> Dict[str, Any]:
    """Analyse d'email avec IA en fallback."""
    try:
        from langchain_openai import ChatOpenAI

        llm = ChatOpenAI(model_name="gpt-4o", temperature=0.1)

        prompt = f"""
        Analyse ce contenu d'email et extrait les entités importantes :

        CONTENU: {contenu_email}

        Extrait et structure les informations suivantes (JSON) :
        {{
            "prestations": [
                {{
                    "description": "Description de la prestation",
                    "quantite": 1,
                    "prix_unitaire": 0,
                    "prix_total": 0
                }}
            ],
            "conditions_paiement": {{
                "delai": "30 jours",
                "modalite": "virement"
            }},
            "contact": {{
                "nom": "Nom du contact",
                "email": "<EMAIL>"
            }},
            "montants": {{
                "total_ht": 0,
                "tva": 0,
                "total_ttc": 0
            }}
        }}

        Réponds UNIQUEMENT avec le JSON, sans texte supplémentaire.
        """

        response = llm.invoke(prompt)
        reponse_text = getattr(response, "content", str(response))

        # Parser le JSON
        import json
        import re

        json_match = re.search(r'\{.*\}', reponse_text, re.DOTALL)
        if json_match:
            try:
                entites = json.loads(json_match.group())
                return {
                    'source': 'ia_fallback',
                    'texte': contenu_email,
                    'entites': entites,
                    'tableaux': [],
                    'success': True
                }
            except json.JSONDecodeError:
                pass

    except Exception as e:
        logger.error(f"❌ Erreur analyse IA fallback: {e}")

    # Fallback minimal
    return {
        'source': 'minimal',
        'texte': contenu_email,
        'entites': {},
        'tableaux': [],
        'success': False
    }

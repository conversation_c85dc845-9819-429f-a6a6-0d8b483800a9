
# ingestion/document_ingestion.py
# Module d'ingestion de documents utilisant TensorLake

import os
import logging
from typing import List, Dict, Any, Optional, Union, Literal
from pathlib import Path
import json
import datetime
import pandas as pd
import csv

from integrations.tensorlake_tools import TensorLakeClient, analyser_document_avec_tensorlake

# Configuration du logging
logger = logging.getLogger(__name__)

def lire_documents(
    dossier_path: Optional[str] = None,
    extensions: Optional[List[str]] = None,
    max_documents: int = 100,
    force_reanalyse: bool = False
) -> List[Dict[str, Any]]:
    """
    Lit et analyse les documents d'un dossier avec TensorLake.
    
    Args:
        dossier_path: Chemin vers le dossier contenant les documents (par défaut: data/documents)
        extensions: Liste des extensions de fichiers à traiter (par défaut: ['.pdf', '.docx', '.xlsx'])
        max_documents: Nombre maximum de documents à traiter
        force_reanalyse: Si True, force la réanalyse même si un cache existe
        
    Returns:
        Liste de documents analysés avec leurs métadonnées
    """
    try:
        # Valeurs par défaut
        dossier_path = dossier_path or "data/documents"
        extensions = extensions or ['.pdf', '.docx', '.xlsx', '.jpg', '.png']
        
        # Créer le dossier s'il n'existe pas
        os.makedirs(dossier_path, exist_ok=True)
        
        # Récupérer la liste des fichiers
        dossier = Path(dossier_path)
        fichiers = []
        for ext in extensions:
            fichiers.extend(list(dossier.glob(f"**/*{ext}")))
        
        # Limiter le nombre de fichiers
        fichiers = fichiers[:max_documents]
        
        if not fichiers:
            logger.warning(f"Aucun document trouvé dans {dossier_path} avec les extensions {extensions}")
            return []
        
        logger.info(f"Traitement de {len(fichiers)} documents...")
        
        # Analyser chaque document
        documents_analyses = []
        for fichier in fichiers:
            try:
                # Vérifier si un cache existe pour ce fichier
                cache_path = fichier.with_suffix('.tensorlake.json')
                if not force_reanalyse and cache_path.exists():
                    # Vérifier si le cache est récent (moins de 7 jours)
                    cache_time = datetime.datetime.fromtimestamp(cache_path.stat().st_mtime)
                    if (datetime.datetime.now() - cache_time).days < 7:
                        # Utiliser le cache
                        with open(cache_path, 'r', encoding='utf-8') as f:
                            analyse = json.load(f)
                            logger.info(f"Utilisation du cache pour {fichier.name}")
                            documents_analyses.append({
                                "path": str(fichier),
                                "nom": fichier.name,
                                "extension": fichier.suffix,
                                "taille": fichier.stat().st_size,
                                "date_modification": datetime.datetime.fromtimestamp(
                                    fichier.stat().st_mtime).isoformat(),
                                "analyse": analyse
                            })
                            continue
                
                # Analyser avec TensorLake
                logger.info(f"Analyse de {fichier.name} avec TensorLake...")
                analyse = analyser_document_avec_tensorlake(str(fichier))
                
                # Vérifier si l'analyse a réussi
                if "error" in analyse:
                    logger.warning(f"⚠️ Erreur lors de l'analyse de {fichier.name}: {analyse['error']}")
                    # Continuer avec le fichier suivant si l'analyse a échoué
                    continue
                
                # Sauvegarder dans le cache
                with open(cache_path, 'w', encoding='utf-8') as f:
                    json.dump(analyse, f, indent=2, ensure_ascii=False)
                
                # Ajouter aux résultats
                documents_analyses.append({
                    "path": str(fichier),
                    "nom": fichier.name,
                    "extension": fichier.suffix,
                    "taille": fichier.stat().st_size,
                    "date_modification": datetime.datetime.fromtimestamp(
                        fichier.stat().st_mtime).isoformat(),
                    "analyse": analyse
                })
                
            except Exception as e:
                logger.error(f"❌ Erreur lors de l'analyse de {fichier.name}: {e}")
                # Continuer avec le fichier suivant
        
        logger.info(f"✅ {len(documents_analyses)} documents analysés avec succès")
        return documents_analyses
        
    except Exception as e:
        logger.error(f"❌ Erreur lors de la lecture des documents: {e}")
        return []

def extraire_texte_document(fichier_path: Union[str, Path]) -> str:
    """
    Extrait le texte d'un document avec TensorLake.
    
    Args:
        fichier_path: Chemin vers le fichier à analyser
        
    Returns:
        Texte extrait du document
    """
    try:
        analyse = analyser_document_avec_tensorlake(str(fichier_path))
        return analyse.get("texte", "")
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'extraction du texte: {e}")
        return ""

def extraire_tableaux_document(fichier_path: Union[str, Path]) -> List[Dict[str, Any]]:
    """
    Extrait les tableaux d'un document avec TensorLake.
    
    Args:
        fichier_path: Chemin vers le fichier à analyser
        
    Returns:
        Liste des tableaux extraits
    """
    try:
        analyse = analyser_document_avec_tensorlake(str(fichier_path))
        return analyse.get("tableaux", [])
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'extraction des tableaux: {e}")
        return []

def extraire_entites_document(fichier_path: Union[str, Path]) -> Dict[str, List[Dict[str, Any]]]:
    """
    Extrait les entités nommées d'un document avec TensorLake.
    
    Args:
        fichier_path: Chemin vers le fichier à analyser
        
    Returns:
        Dictionnaire des entités par type
    """
    try:
        analyse = analyser_document_avec_tensorlake(str(fichier_path))
        return analyse.get("entites", {})
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'extraction des entités: {e}")
        return {}

def generer_resume_document(fichier_path: Union[str, Path], max_longueur: int = 200) -> str:
    """
    Génère un résumé court du document en utilisant les entités et le début du texte.
    
    Args:
        fichier_path: Chemin vers le fichier à analyser
        max_longueur: Longueur maximale du résumé
        
    Returns:
        Résumé du document
    """
    try:
        analyse = analyser_document_avec_tensorlake(str(fichier_path))
        
        # Extraire le début du texte
        texte = analyse.get("texte", "")
        debut_texte = texte[:max_longueur] + "..." if len(texte) > max_longueur else texte
        
        # Extraire les entités principales
        entites = analyse.get("entites", {})
        entites_principales = []
        
        # Priorité aux personnes, organisations, lieux
        for type_entite in ["PERSON", "ORG", "LOC", "DATE", "MONEY"]:
            if type_entite in entites and entites[type_entite]:
                # Prendre les 3 premières entités de chaque type
                for entite in entites[type_entite][:3]:
                    entites_principales.append(entite.get("text", ""))
        
        # Limiter à 5 entités au total
        entites_principales = entites_principales[:5]
        
        # Construire le résumé
        if entites_principales:
            entites_str = ", ".join(entites_principales)
            resume = f"{debut_texte}\n\nEntités principales: {entites_str}"
        else:
            resume = debut_texte
        
        return resume
        
    except Exception as e:
        logger.error(f"❌ Erreur lors de la génération du résumé: {e}")
        return ""

def exporter_documents(
    documents_analyses: List[Dict[str, Any]],
    format_sortie: Literal["csv", "excel"] = "csv",
    chemin_sortie: Optional[str] = None,
    inclure_texte: bool = False,
    inclure_resume: bool = True,
    inclure_entites: bool = True
) -> str:
    """
    Exporte la liste des documents analysés en CSV ou Excel.
    
    Args:
        documents_analyses: Liste de documents analysés (format de lire_documents)
        format_sortie: Format de sortie ("csv" ou "excel")
        chemin_sortie: Chemin du fichier de sortie (par défaut: "data/exports/documents_YYYYMMDD.{ext}")
        inclure_texte: Si True, inclut le texte complet des documents (peut être volumineux)
        inclure_resume: Si True, inclut un résumé généré du document
        inclure_entites: Si True, inclut les entités principales détectées
        
    Returns:
        Chemin du fichier exporté
    """
    try:
        # Créer le dossier d'export s'il n'existe pas
        export_dir = "data/exports"
        os.makedirs(export_dir, exist_ok=True)
        
        # Déterminer le chemin de sortie
        if not chemin_sortie:
            date_str = datetime.datetime.now().strftime("%Y%m%d")
            ext = "xlsx" if format_sortie == "excel" else "csv"
            chemin_sortie = f"{export_dir}/documents_{date_str}.{ext}"
        
        # Préparer les données pour l'export
        export_data = []
        for doc in documents_analyses:
            # Données de base
            doc_export = {
                "nom": doc["nom"],
                "chemin": doc["path"],
                "extension": doc["extension"],
                "taille_ko": round(doc["taille"] / 1024, 2),
                "date_modification": doc["date_modification"],
                "nb_entites": sum(len(entites) for entites in doc["analyse"].get("entites", {}).values()),
                "nb_tableaux": len(doc["analyse"].get("tableaux", [])),
            }
            
            # Ajouter le texte si demandé
            if inclure_texte:
                doc_export["texte"] = doc["analyse"].get("texte", "")[:1000] + "..." if len(doc["analyse"].get("texte", "")) > 1000 else doc["analyse"].get("texte", "")
            
            # Ajouter le résumé si demandé
            if inclure_resume:
                # Générer un résumé à partir du texte et des entités
                texte = doc["analyse"].get("texte", "")
                if texte:
                    debut_texte = texte[:200] + "..." if len(texte) > 200 else texte
                    doc_export["resume"] = debut_texte
            
            # Ajouter les entités principales (jusqu'à 3 de chaque type)
            if inclure_entites:
                for type_entite, entites in doc["analyse"].get("entites", {}).items():
                    entites_str = ", ".join([e.get("text", "") for e in entites[:3]])
                    if len(entites) > 3:
                        entites_str += f" et {len(entites) - 3} autres"
                    doc_export[f"entites_{type_entite}"] = entites_str
            
            export_data.append(doc_export)
        
        # Exporter selon le format demandé
        if format_sortie == "excel":
            df = pd.DataFrame(export_data)
            df.to_excel(chemin_sortie, index=False, engine="openpyxl")
        else:  # csv
            df = pd.DataFrame(export_data)
            df.to_csv(chemin_sortie, index=False, encoding="utf-8-sig")
        
        logger.info(f"✅ {len(export_data)} documents exportés vers {chemin_sortie}")
        return chemin_sortie
        
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'export des documents: {e}")
        raise



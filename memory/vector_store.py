
# memory/vector_store.py
# Module de vectorisation et stockage des documents

import os
import logging
from typing import List, Dict, Any, Optional, Union
import json
import datetime
from pathlib import Path

from langchain_openai import OpenAIEmbeddings
from langchain_community.vectorstores import Chroma
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.schema import Document

# Configuration du logging
logger = logging.getLogger(__name__)

class GestionnaireBDD:
    """Gestionnaire de base de données vectorielle pour les documents."""
    
    def __init__(self, 
                persist_directory: str = "data/vectorstore",
                embedding_model: str = "text-embedding-3-small"):
        """
        Initialise le gestionnaire de BDD vectorielle.
        
        Args:
            persist_directory: Répertoire de persistance de la BDD
            embedding_model: Modèle d'embedding à utiliser
        """
        self.persist_directory = persist_directory
        
        # Créer le répertoire s'il n'existe pas
        os.makedirs(persist_directory, exist_ok=True)
        
        # Initialiser les embeddings
        self.embeddings = OpenAIEmbeddings(model=embedding_model)
        
        # Initialiser le text splitter
        self.text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=1000,
            chunk_overlap=100
        )
        
        # Charger ou créer la BDD vectorielle
        try:
            self.vectorstore = Chroma(
                persist_directory=persist_directory,
                embedding_function=self.embeddings
            )
            logger.info(f"✅ Base de données vectorielle chargée depuis {persist_directory}")
        except Exception as e:
            logger.warning(f"⚠️ Impossible de charger la BDD vectorielle: {e}")
            logger.info("Création d'une nouvelle BDD vectorielle")
            self.vectorstore = Chroma(
                persist_directory=persist_directory,
                embedding_function=self.embeddings
            )
    
    def ajouter_documents(self, documents_analyses: List[Dict[str, Any]]) -> int:
        """
        Ajoute des documents analysés à la BDD vectorielle.
        
        Args:
            documents_analyses: Liste de documents analysés (format de lire_documents)
            
        Returns:
            Nombre de chunks ajoutés à la BDD
        """
        try:
            # Convertir les documents analysés en format Document de LangChain
            documents = []
            for doc in documents_analyses:
                # Extraire le texte de l'analyse
                texte = doc["analyse"].get("texte", "")
                if not texte:
                    logger.warning(f"⚠️ Document sans texte: {doc['nom']}")
                    continue
                
                # Créer les métadonnées
                metadata = {
                    "source": doc["path"],
                    "nom": doc["nom"],
                    "extension": doc["extension"],
                    "date_modification": doc["date_modification"],
                    "entites": json.dumps(doc["analyse"].get("entites", {}))
                }
                
                # Ajouter le document
                documents.append(Document(page_content=texte, metadata=metadata))
            
            # Diviser les documents en chunks
            chunks = self.text_splitter.split_documents(documents)
            logger.info(f"Documents divisés en {len(chunks)} chunks")
            
            # Ajouter les chunks à la BDD vectorielle
            self.vectorstore.add_documents(chunks)
            
            # Persister la BDD
            self.vectorstore.persist()
            
            logger.info(f"✅ {len(chunks)} chunks ajoutés à la BDD vectorielle")
            return len(chunks)
            
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'ajout des documents à la BDD: {e}")
            return 0
    
    def rechercher(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Recherche des documents similaires à la requête.
        
        Args:
            query: Requête de recherche
            k: Nombre de résultats à retourner
            
        Returns:
            Liste des documents similaires avec score
        """
        try:
            resultats = self.vectorstore.similarity_search_with_score(query, k=k)
            
            # Formater les résultats
            documents = []
            for doc, score in resultats:
                documents.append({
                    "contenu": doc.page_content,
                    "score": float(score),
                    "source": doc.metadata.get("source", ""),
                    "nom": doc.metadata.get("nom", ""),
                    "entites": json.loads(doc.metadata.get("entites", "{}"))
                })
            
            return documents
        except Exception as e:
            logger.error(f"❌ Erreur lors de la recherche: {e}")
            return []
    
    def supprimer_document(self, source: str) -> bool:
        """
        Supprime un document de la BDD vectorielle.
        
        Args:
            source: Chemin source du document à supprimer
            
        Returns:
            True si suppression réussie, False sinon
        """
        try:
            self.vectorstore.delete(where={"source": source})
            self.vectorstore.persist()
            logger.info(f"✅ Document supprimé: {source}")
            return True
        except Exception as e:
            logger.error(f"❌ Erreur lors de la suppression du document: {e}")
            return False
    
    def statistiques(self) -> Dict[str, Any]:
        """
        Retourne des statistiques sur la BDD vectorielle.
        
        Returns:
            Statistiques de la BDD
        """
        try:
            # Obtenir le nombre de documents
            # Note: Cette méthode peut varier selon la version de Chroma
            # et comment elle expose ses collections
            collection = self.vectorstore._collection
            count = 0
            try:
                count = collection.count()
            except:
                # Fallback si count() n'est pas disponible
                count = len(self.vectorstore.get()["ids"]) if self.vectorstore.get() else 0
            
            return {
                "nombre_documents": count,
                "repertoire": self.persist_directory,
                "date": datetime.datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"❌ Erreur lors de la récupération des statistiques: {e}")
            return {"error": str(e)}

# Fonction principale pour stocker les vecteurs
def stocker_vecteurs(documents_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Stocke les vecteurs des documents analysés.
    
    Args:
        documents_analyses: Liste de documents analysés (format de lire_documents)
        
    Returns:
        Informations sur le stockage
    """
    try:
        gestionnaire = GestionnaireBDD()
        nb_chunks = gestionnaire.ajouter_documents(documents_analyses)
        
        return {
            "status": "success",
            "nb_documents": len(documents_analyses),
            "nb_chunks": nb_chunks,
            "timestamp": datetime.datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"❌ Erreur lors du stockage des vecteurs: {e}")
        return {
            "status": "error",
            "error": str(e),
            "timestamp": datetime.datetime.now().isoformat()
        }


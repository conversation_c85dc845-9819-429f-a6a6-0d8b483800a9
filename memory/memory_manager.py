# memory/memory_manager.py
# Gestionnaire de mémoire pour l'agent

import os
import logging
import json
import datetime
from typing import List, Dict, Any, Optional, Union
from pathlib import Path

from memory.vector_store import GestionnaireBDD

# Configuration du logging
logger = logging.getLogger(__name__)

class GestionnaireMemoire:
    """Gestionnaire de mémoire pour l'agent."""
    
    def __init__(self, 
                vectorstore_dir: str = "data/vectorstore",
                cache_dir: str = "data/cache"):
        """
        Initialise le gestionnaire de mémoire.
        
        Args:
            vectorstore_dir: Répertoire de la BDD vectorielle
            cache_dir: Répertoire de cache
        """
        self.vectorstore_dir = vectorstore_dir
        self.cache_dir = cache_dir
        
        # Créer les répertoires s'ils n'existent pas
        os.makedirs(vectorstore_dir, exist_ok=True)
        os.makedirs(cache_dir, exist_ok=True)
        
        # Initialiser la BDD vectorielle
        try:
            self.bdd = GestionnaireBDD(persist_directory=vectorstore_dir)
            logger.info(f"✅ BDD vectorielle initialisée dans {vectorstore_dir}")
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'initialisation de la BDD vectorielle: {e}")
            raise
        
        # Fichier de suivi des documents
        self.suivi_path = Path(cache_dir) / "suivi_documents.json"
        self.suivi = self._charger_suivi()
    
    def _charger_suivi(self) -> Dict[str, Any]:
        """
        Charge le fichier de suivi des documents.
        
        Returns:
            Données de suivi
        """
        if self.suivi_path.exists():
            try:
                with open(self.suivi_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"❌ Erreur lors du chargement du suivi: {e}")
        
        # Créer un suivi par défaut
        return {
            "documents": {},
            "derniere_mise_a_jour": datetime.datetime.now().isoformat()
        }
    
    def _sauvegarder_suivi(self) -> None:
        """
        Sauvegarde le fichier de suivi des documents.
        """
        try:
            with open(self.suivi_path, 'w', encoding='utf-8') as f:
                json.dump(self.suivi, f, indent=2, ensure_ascii=False)
            logger.info(f"✅ Suivi sauvegardé dans {self.suivi_path}")
        except Exception as e:
            logger.error(f"❌ Erreur lors de la sauvegarde du suivi: {e}")
    
    def memoriser_documents(self, documents_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Mémorise des documents analysés.
        
        Args:
            documents_analyses: Liste de documents analysés (format de lire_documents)
            
        Returns:
            Informations sur la mémorisation
        """
        try:
            # Vérifier si documents_analyses est une liste
            if not isinstance(documents_analyses, list):
                logger.error("❌ documents_analyses doit être une liste")
                return {"status": "error", "error": "documents_analyses doit être une liste"}
            
            # Vérifier si la liste est vide
            if not documents_analyses:
                logger.warning("⚠️ Liste de documents vide")
                return {"status": "warning", "message": "Liste de documents vide"}
            
            # Ajouter les documents à la BDD vectorielle
            nb_chunks = self.bdd.ajouter_documents(documents_analyses)
            
            # Mettre à jour le suivi
            for doc in documents_analyses:
                path = doc.get("path", "")
                if not path:
                    logger.warning(f"⚠️ Document sans chemin: {doc.get('nom', 'inconnu')}")
                    continue
                
                self.suivi["documents"][path] = {
                    "nom": doc.get("nom", ""),
                    "date_ajout": datetime.datetime.now().isoformat(),
                    "date_modification": doc.get("date_modification", ""),
                    "entites": doc.get("analyse", {}).get("entites", {})
                }
            
            # Mettre à jour la date de dernière mise à jour
            self.suivi["derniere_mise_a_jour"] = datetime.datetime.now().isoformat()
            
            # Sauvegarder le suivi
            self._sauvegarder_suivi()
            
            return {
                "status": "success",
                "nb_documents": len(documents_analyses),
                "nb_chunks": nb_chunks,
                "timestamp": datetime.datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"❌ Erreur lors de la mémorisation des documents: {e}")
            return {"status": "error", "error": str(e)}
    
    def rechercher_documents(self, query: str, k: int = 5) -> List[Dict[str, Any]]:
        """
        Recherche des documents similaires à la requête.
        
        Args:
            query: Requête de recherche
            k: Nombre de résultats à retourner
            
        Returns:
            Liste des documents similaires avec leur score
        """
        try:
            if not query:
                logger.warning("⚠️ Requête de recherche vide")
                return []
            
            return self.bdd.rechercher(query, k=k)
        except Exception as e:
            logger.error(f"❌ Erreur lors de la recherche de documents: {e}")
            return []
    
    def obtenir_statistiques(self) -> Dict[str, Any]:
        """
        Retourne des statistiques sur la mémoire.
        
        Returns:
            Statistiques de la mémoire
        """
        try:
            stats_bdd = self.bdd.statistiques()
            
            return {
                "bdd_vectorielle": stats_bdd,
                "nb_documents_suivis": len(self.suivi["documents"]),
                "derniere_mise_a_jour": self.suivi["derniere_mise_a_jour"]
            }
        except Exception as e:
            logger.error(f"❌ Erreur lors de la récupération des statistiques: {e}")
            return {"error": str(e)}
    
    def supprimer_document(self, path: str) -> bool:
        """
        Supprime un document de la mémoire.
        
        Args:
            path: Chemin du document à supprimer
            
        Returns:
            True si la suppression a réussi, False sinon
        """
        try:
            if not path:
                logger.warning("⚠️ Chemin de document vide")
                return False
            
            # Supprimer de la BDD vectorielle
            resultat = self.bdd.supprimer_document(path)
            
            # Si la suppression a réussi, supprimer du suivi
            if resultat and path in self.suivi["documents"]:
                del self.suivi["documents"][path]
                self._sauvegarder_suivi()
            
            return resultat
        except Exception as e:
            logger.error(f"❌ Erreur lors de la suppression du document: {e}")
            return False

def gerer_memorisation(documents_analyses: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Gère la mémorisation des documents analysés.
    
    Args:
        documents_analyses: Liste de documents analysés (format de lire_documents)
        
    Returns:
        Informations sur la mémorisation
    """
    try:
        # Initialiser le gestionnaire de mémoire
        gestionnaire = GestionnaireMemoire()
        
        # Mémoriser les documents
        return gestionnaire.memoriser_documents(documents_analyses)
    except Exception as e:
        logger.error(f"❌ Erreur lors de la gestion de la mémorisation: {e}")
        return {"status": "error", "error": str(e)}

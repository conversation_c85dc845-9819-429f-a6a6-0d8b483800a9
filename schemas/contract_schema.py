
# schemas/contract_schema.py
# Définition du schéma de contrat avec Pydantic

from datetime import datetime, date
from typing import List, Dict, Optional, Any
from enum import Enum
from pydantic import BaseModel, Field, field_validator, model_validator
import uuid

class StatutContrat(str, Enum):
    """Énumération des statuts possibles pour un contrat."""
    BROUILLON = "brouillon"
    EN_ATTENTE = "en_attente"
    ACTIF = "actif"
    SUSPENDU = "suspendu"
    TERMINE = "termine"
    EXPIRE = "expire"
    RESILIE = "resilie"

class TypeContrat(str, Enum):
    """Énumération des types de contrats."""
    SERVICE = "service"
    PRODUIT = "produit"
    MAINTENANCE = "maintenance"
    LICENCE = "licence"
    ABONNEMENT = "abonnement"
    AUTRE = "autre"

class Contact(BaseModel):
    """Schéma pour les contacts associés au contrat."""
    nom: str = Field(..., description="Nom du contact")
    prenom: Optional[str] = Field(None, description="Prénom du contact")
    email: Optional[str] = Field(None, description="Adresse email du contact")
    telephone: Optional[str] = Field(None, description="Numéro de téléphone du contact")
    role: Optional[str] = Field(None, description="Rôle du contact (ex: signataire, contact technique)")

class Paiement(BaseModel):
    """Schéma pour les informations de paiement."""
    montant: float = Field(..., description="Montant du paiement")
    devise: str = Field("EUR", description="Devise du paiement (code ISO)")
    date_paiement: Optional[date] = Field(None, description="Date du paiement")
    methode: Optional[str] = Field(None, description="Méthode de paiement")
    statut: Optional[str] = Field(None, description="Statut du paiement")
    reference: Optional[str] = Field(None, description="Référence du paiement")

class Clause(BaseModel):
    """Schéma pour les clauses du contrat."""
    titre: str = Field(..., description="Titre de la clause")
    contenu: str = Field(..., description="Contenu de la clause")
    importance: Optional[str] = Field(None, description="Niveau d'importance (critique, standard, informative)")

class ContractSchema(BaseModel):
    """Schéma principal pour les contrats."""
    id: str = Field(default_factory=lambda: str(uuid.uuid4()), description="Identifiant unique du contrat")
    reference: Optional[str] = Field(None, description="Référence externe du contrat")
    titre: str = Field(..., description="Titre du contrat")
    description: Optional[str] = Field(None, description="Description du contrat")
    type: TypeContrat = Field(..., description="Type de contrat")
    statut: StatutContrat = Field(StatutContrat.BROUILLON, description="Statut actuel du contrat")
    
    # Dates importantes
    date_creation: datetime = Field(default_factory=datetime.now, description="Date de création du contrat")
    date_signature: Optional[date] = Field(None, description="Date de signature du contrat")
    date_debut: Optional[date] = Field(None, description="Date de début du contrat")
    date_fin: Optional[date] = Field(None, description="Date de fin du contrat")
    date_renouvellement: Optional[date] = Field(None, description="Date de renouvellement automatique")
    
    # Parties impliquées
    client: Dict[str, Any] = Field(..., description="Informations sur le client")
    fournisseur: Dict[str, Any] = Field(..., description="Informations sur le fournisseur")
    contacts: List[Contact] = Field(default_factory=list, description="Liste des contacts associés")
    
    # Informations financières
    montant_total: float = Field(..., description="Montant total du contrat")
    devise: str = Field("EUR", description="Devise du contrat (code ISO)")
    frequence_paiement: Optional[str] = Field(None, description="Fréquence des paiements (mensuel, annuel, etc.)")
    paiements: List[Paiement] = Field(default_factory=list, description="Historique des paiements")
    
    # Contenu du contrat
    clauses: List[Clause] = Field(default_factory=list, description="Clauses du contrat")
    documents_associes: List[str] = Field(default_factory=list, description="Liste des documents associés (chemins ou URLs)")
    
    # Métadonnées
    tags: List[str] = Field(default_factory=list, description="Tags pour la catégorisation")
    notes: Optional[str] = Field(None, description="Notes internes sur le contrat")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Métadonnées additionnelles")
    
    class Config:
        """Configuration du modèle Pydantic."""
        validate_assignment = True
        extra = "allow"
        json_encoders = {
            datetime: lambda v: v.isoformat(),
            date: lambda v: v.isoformat()
        }
    
    @field_validator('date_fin')
    @classmethod
    def date_fin_apres_debut(cls, v, info):
        """Valide que la date de fin est après la date de début."""
        if v and info.data.get('date_debut') and v < info.data['date_debut']:
            raise ValueError("La date de fin doit être postérieure à la date de début")
        return v
    
    @model_validator(mode='after')
    def verifier_coherence_dates(self):
        """Vérifie la cohérence globale des dates."""
        # Si le contrat est actif, il doit avoir une date de début
        if self.statut == StatutContrat.ACTIF and not self.date_debut:
            raise ValueError("Un contrat actif doit avoir une date de début")

        # Si le contrat est terminé ou expiré, il doit avoir une date de fin
        if self.statut in [StatutContrat.TERMINE, StatutContrat.EXPIRE] and not self.date_fin:
            raise ValueError("Un contrat terminé ou expiré doit avoir une date de fin")

        return self
    
    def est_actif(self) -> bool:
        """Vérifie si le contrat est actuellement actif."""
        if self.statut != StatutContrat.ACTIF:
            return False
        
        aujourd_hui = date.today()
        if self.date_debut and self.date_debut > aujourd_hui:
            return False
        
        if self.date_fin and self.date_fin < aujourd_hui:
            return False
        
        return True
    
    def jours_avant_expiration(self) -> Optional[int]:
        """Calcule le nombre de jours avant l'expiration du contrat."""
        if not self.date_fin:
            return None
        
        aujourd_hui = date.today()
        if self.date_fin < aujourd_hui:
            return -1  # Déjà expiré
        
        delta = self.date_fin - aujourd_hui
        return delta.days
    
    def to_dict(self) -> Dict[str, Any]:
        """Convertit le modèle en dictionnaire."""
        return self.model_dump(by_alias=True)
    
    def resume(self) -> Dict[str, Any]:
        """Génère un résumé du contrat avec les informations essentielles."""
        return {
            "id": self.id,
            "reference": self.reference,
            "titre": self.titre,
            "type": self.type,
            "statut": self.statut,
            "client": self.client.get("nom", ""),
            "montant_total": self.montant_total,
            "devise": self.devise,
            "date_debut": self.date_debut,
            "date_fin": self.date_fin,
            "jours_avant_expiration": self.jours_avant_expiration(),
            "est_actif": self.est_actif()
        }


# test_email_config.py
# Test de configuration email pour l'agent

import os
import sys
from dotenv import load_dotenv

print("🔍 TEST DE CONFIGURATION EMAIL")
print("=" * 50)

# Charger les variables d'environnement
load_dotenv()

# Vérifier les variables
email = os.getenv("AGENT_EMAIL_ADDRESS")
password = os.getenv("AGENT_EMAIL_PASSWORD")

print(f"📧 Email configuré: {email}")
print(f"🔑 Mot de passe configuré: {'✅ OUI' if password else '❌ NON'}")

if password:
    password_clean = password.replace(' ', '')
    print(f"🔢 Longueur du mot de passe: {len(password_clean)} caractères")
    
    if len(password_clean) == 16:
        print("✅ Longueur correcte pour un mot de passe d'application Gmail")
    else:
        print("⚠️ Longueur incorrecte (attendu: 16 caractères)")

# Vérifications de base
print("\n🔍 VÉRIFICATIONS:")

if not email:
    print("❌ AGENT_EMAIL_ADDRESS non défini dans .env")
    sys.exit(1)

if not password:
    print("❌ AGENT_EMAIL_PASSWORD non défini dans .env")
    sys.exit(1)

if "@gmail.com" not in email:
    print("⚠️ L'email ne semble pas être un compte Gmail")

print("✅ Configuration de base OK - Prêt pour le test de connexion")

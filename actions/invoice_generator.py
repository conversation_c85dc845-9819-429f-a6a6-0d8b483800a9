# actions/invoice_generator.py
# Générateur de factures PDF pour l'agent

import os
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import json

# Pour la génération de PDF
from reportlab.lib.pagesizes import A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT

# Configuration du logging
logger = logging.getLogger(__name__)

def _extraire_services_depuis_contenu(contenu: str) -> List[Dict[str, Any]]:
    """
    NOUVELLE VERSION: Extrait intelligemment les services avec TensorLake + IA.

    Args:
        contenu: Contenu de l'email

    Returns:
        Liste des articles/services détectés avec précision
    """
    logger.info("🔍 Extraction intelligente des services avec TensorLake + IA")

    # ÉTAPE 1: Tentative d'extraction avec TensorLake
    articles_tensorlake = _extraire_avec_tensorlake(contenu)
    if articles_tensorlake:
        logger.info(f"✅ TensorLake a extrait {len(articles_tensorlake)} services")
        return articles_tensorlake

    # ÉTAPE 2: Extraction avec IA avancée (fallback)
    articles_ia = _extraire_avec_ia_avancee(contenu)
    if articles_ia:
        logger.info(f"✅ IA avancée a extrait {len(articles_ia)} services")
        return articles_ia

    # ÉTAPE 3: Extraction par patterns améliorés (dernier recours)
    articles_patterns = _extraire_avec_patterns_avances(contenu)
    if articles_patterns:
        logger.info(f"✅ Patterns avancés ont extrait {len(articles_patterns)} services")
        return articles_patterns

    logger.warning("⚠️ Aucun service détecté avec les méthodes intelligentes")
    return []

def _extraire_avec_tensorlake(contenu: str) -> List[Dict[str, Any]]:
    """Extraction avec TensorLake pour une précision maximale."""
    try:
        from integrations.tensorlake_tools import TensorLakeAnalyzer

        # Créer une instance TensorLake
        analyzer = TensorLakeAnalyzer()

        # Créer un fichier temporaire pour l'analyse
        import tempfile
        import os

        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write(contenu)
            temp_file = f.name

        try:
            # Analyser le contenu avec TensorLake
            analyse = analyzer.analyser_document(temp_file)
        finally:
            # Nettoyer le fichier temporaire
            os.unlink(temp_file)

        if not analyse or 'entites' not in analyse:
            return []

        articles = []
        entites = analyse['entites']

        # Extraire les prestations détectées par TensorLake
        prestations = entites.get('prestations', [])
        for prestation in prestations:
            # Validation des données extraites
            if _valider_prestation_tensorlake(prestation):
                article = {
                    'description': prestation.get('description', '').strip(),
                    'quantite': float(prestation.get('quantite', 1)),
                    'prix_unitaire': float(prestation.get('prix_unitaire', 0))
                }

                # Calculer le prix unitaire si on a le total
                if article['prix_unitaire'] == 0 and prestation.get('prix_total'):
                    article['prix_unitaire'] = float(prestation['prix_total']) / article['quantite']

                articles.append(article)

        # Filtrer les doublons et valider la cohérence
        articles = _nettoyer_articles_extraits(articles)

        return articles

    except Exception as e:
        logger.warning(f"⚠️ TensorLake indisponible: {e}")
        return []

def _valider_prestation_tensorlake(prestation: Dict) -> bool:
    """Valide qu'une prestation extraite par TensorLake est cohérente."""
    # Vérifier les champs obligatoires
    if not prestation.get('description'):
        return False

    # Exclure les conditions de paiement
    description_lower = prestation.get('description', '').lower()
    mots_exclus = [
        'paiement', 'virement', 'chèque', 'espèces', 'carte', 'délai',
        'conditions', 'modalités', 'échéance', 'acompte', 'solde'
    ]

    if any(mot in description_lower for mot in mots_exclus):
        logger.info(f"🚫 Prestation exclue (condition de paiement): {prestation.get('description')}")
        return False

    # Vérifier la cohérence des montants
    quantite = prestation.get('quantite', 1)
    prix_unitaire = prestation.get('prix_unitaire', 0)
    prix_total = prestation.get('prix_total', 0)

    if quantite <= 0:
        return False

    if prix_unitaire > 0 and prix_total > 0:
        # Vérifier la cohérence quantité × prix_unitaire = prix_total
        total_calcule = quantite * prix_unitaire
        if abs(total_calcule - prix_total) > 0.01:  # Tolérance de 1 centime
            logger.warning(f"⚠️ Incohérence montants: {quantite} × {prix_unitaire} ≠ {prix_total}")
            return False

    return True

def _extraire_avec_ia_avancee(contenu: str) -> List[Dict[str, Any]]:
    """Extraction avec IA avancée et prompt optimisé."""
    try:
        from langchain_openai import ChatOpenAI
        from langchain.prompts import PromptTemplate

        llm = ChatOpenAI(model_name="gpt-4o", temperature=0.1)

        prompt = PromptTemplate(
            input_variables=["contenu"],
            template="""
            Tu es un expert comptable spécialisé dans l'extraction de données de facturation.

            CONTENU EMAIL:
            {contenu}

            MISSION: Extraire UNIQUEMENT les prestations/services à facturer.

            RÈGLES STRICTES:
            1. IGNORER les conditions de paiement (délais, modalités, virements, etc.)
            2. IGNORER les formules de politesse et contexte général
            3. EXTRAIRE seulement les prestations avec description + montant/quantité
            4. DISTINGUER clairement prestations vs informations

            FORMAT DE RÉPONSE (JSON strict):
            [
                {{
                    "description": "Description exacte de la prestation",
                    "quantite": nombre_ou_1,
                    "prix_unitaire": montant_unitaire_en_euros,
                    "prix_total": montant_total_en_euros
                }}
            ]

            EXEMPLES:
            - "location pata 2050€" → {{"description": "Location pata", "quantite": 1, "prix_unitaire": 2050, "prix_total": 2050}}
            - "émulsion 2 tonnes 1400€" → {{"description": "Émulsion", "quantite": 2, "prix_unitaire": 700, "prix_total": 1400}}
            - "paiement 45 jours" → IGNORER (condition de paiement)

            Si aucune prestation claire n'est détectée, retourner: []

            RÉPONSE JSON:
            """
        )

        chain = prompt | llm
        result = chain.invoke({"contenu": contenu})
        reponse = getattr(result, "content", str(result))

        # Parser la réponse JSON
        import json
        import re

        # Extraire le JSON de la réponse
        json_match = re.search(r'\[.*\]', reponse, re.DOTALL)
        if not json_match:
            return []

        try:
            articles_data = json.loads(json_match.group())
            articles = []

            for item in articles_data:
                if isinstance(item, dict) and item.get('description'):
                    article = {
                        'description': item['description'].strip(),
                        'quantite': float(item.get('quantite', 1)),
                        'prix_unitaire': float(item.get('prix_unitaire', 0))
                    }

                    # Calculer prix_unitaire si on a le total
                    if article['prix_unitaire'] == 0 and item.get('prix_total'):
                        article['prix_unitaire'] = float(item['prix_total']) / article['quantite']

                    if article['prix_unitaire'] > 0:
                        articles.append(article)

            return articles

        except json.JSONDecodeError:
            logger.warning("⚠️ Erreur parsing JSON de l'IA")
            return []

    except Exception as e:
        logger.warning(f"⚠️ IA avancée indisponible: {e}")
        return []

def _extraire_avec_patterns_avances(contenu: str) -> List[Dict[str, Any]]:
    """Extraction avec patterns regex avancés (dernier recours)."""
    import re

    articles = []
    contenu_clean = contenu.lower()

    # Patterns pour prestations avec montants explicites
    patterns_montants = [
        # Format: "description MONTANT€"
        r'([a-zA-ZÀ-ÿ\s]+?)\s+(\d+(?:[.,]\d{2})?)\s*€',
        # Format: "description : MONTANT€"
        r'([a-zA-ZÀ-ÿ\s]+?)\s*:\s*(\d+(?:[.,]\d{2})?)\s*€',
        # Format: "QUANTITÉ unité description MONTANT€"
        r'(\d+(?:[.,]\d+)?)\s+([a-zA-ZÀ-ÿ]+)\s+([a-zA-ZÀ-ÿ\s]+?)\s+(\d+(?:[.,]\d{2})?)\s*€',
    ]

    # Mots à exclure (conditions de paiement, etc.)
    mots_exclus = {
        'paiement', 'virement', 'chèque', 'délai', 'jours', 'conditions',
        'modalités', 'acompte', 'solde', 'échéance', 'total', 'ht', 'ttc'
    }

    for pattern in patterns_montants:
        matches = re.finditer(pattern, contenu_clean)
        for match in matches:
            groups = match.groups()

            if len(groups) == 2:  # description + montant
                description, montant_str = groups
                description = description.strip()

                # Vérifier que ce n'est pas une condition de paiement
                if any(mot in description for mot in mots_exclus):
                    continue

                try:
                    montant = float(montant_str.replace(',', '.'))
                    articles.append({
                        'description': description.title(),
                        'quantite': 1,
                        'prix_unitaire': montant
                    })
                except ValueError:
                    continue

            elif len(groups) == 4:  # quantité + unité + description + montant
                quantite_str, unite, description, montant_str = groups
                description_complete = f"{description} ({unite})".strip()

                # Vérifier que ce n'est pas une condition de paiement
                if any(mot in description_complete for mot in mots_exclus):
                    continue

                try:
                    quantite = float(quantite_str.replace(',', '.'))
                    montant_total = float(montant_str.replace(',', '.'))
                    prix_unitaire = montant_total / quantite if quantite > 0 else montant_total

                    articles.append({
                        'description': description_complete.title(),
                        'quantite': quantite,
                        'prix_unitaire': prix_unitaire
                    })
                except ValueError:
                    continue

    return _nettoyer_articles_extraits(articles)

def _nettoyer_articles_extraits(articles: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """Nettoie et valide les articles extraits."""
    if not articles:
        return []

    articles_nettoyes = []
    descriptions_vues = set()

    for article in articles:
        description = article.get('description', '').strip()
        quantite = article.get('quantite', 0)
        prix_unitaire = article.get('prix_unitaire', 0)

        # Validation de base
        if not description or quantite <= 0 or prix_unitaire <= 0:
            continue

        # Éviter les doublons
        if description.lower() in descriptions_vues:
            continue

        # Validation des montants (pas trop élevés)
        if prix_unitaire > 100000:  # Plus de 100k€ unitaire = suspect
            logger.warning(f"⚠️ Prix unitaire suspect: {prix_unitaire}€ pour {description}")
            continue

        descriptions_vues.add(description.lower())
        articles_nettoyes.append({
            'description': description,
            'quantite': round(quantite, 2),
            'prix_unitaire': round(prix_unitaire, 2)
        })

    logger.info(f"🧹 Articles nettoyés: {len(articles_nettoyes)}/{len(articles)} conservés")
    return articles_nettoyes

def _est_demande_facturation_explicite(contenu: str) -> bool:
    """
    Valide qu'un email contient une demande de facturation explicite.

    Args:
        contenu: Contenu de l'email

    Returns:
        True si c'est une demande de facturation explicite
    """
    contenu_lower = contenu.lower()

    # Mots-clés de commande explicite
    mots_commande_explicite = [
        'facturez', 'facturer', 'envoyer une facture', 'établir une facture',
        'je commande', 'nous commandons', 'procéder à la facturation',
        'merci de facturer', 'veuillez facturer', 'commande ferme'
    ]

    # Vérifier la présence de mots de commande explicite
    commande_explicite = any(mot in contenu_lower for mot in mots_commande_explicite)

    # Vérifier la présence de montants avec descriptions
    import re
    montants_detectes = re.findall(r'[a-zA-ZÀ-ÿ\s]+\s+\d+(?:[.,]\d{2})?\s*€', contenu_lower)

    # Critères de validation
    a_montants_explicites = len(montants_detectes) >= 1
    a_commande_explicite = commande_explicite

    # Mots qui indiquent une simple demande d'info (pas de facturation)
    mots_info_seulement = [
        'combien coûte', 'quel est le prix', 'quels sont vos tarifs',
        'pouvez-vous me dire', 'j\'aimerais savoir', 'information sur'
    ]

    est_demande_info = any(mot in contenu_lower for mot in mots_info_seulement)

    # Décision finale
    if est_demande_info and not a_commande_explicite:
        logger.info("📋 Détecté comme demande d'information, pas de facturation")
        return False

    if a_commande_explicite or (a_montants_explicites and len(contenu) > 50):
        logger.info("✅ Demande de facturation explicite validée")
        return True

    logger.info("❌ Pas de demande de facturation explicite détectée")
    return False

class GenerateurFactures:
    """Générateur de factures PDF professionnel."""
    
    def __init__(self, 
                 entreprise_info: Optional[Dict[str, Any]] = None,
                 template_dir: str = "data/templates"):
        """
        Initialise le générateur de factures.
        
        Args:
            entreprise_info: Informations de l'entreprise
            template_dir: Répertoire des templates
        """
        self.template_dir = template_dir
        os.makedirs(template_dir, exist_ok=True)
        
        # Informations par défaut de l'entreprise
        self.entreprise_info = entreprise_info or self._charger_info_entreprise()
        
        # Styles pour le PDF
        self.styles = getSampleStyleSheet()
        self._creer_styles_personnalises()
        
        logger.info("✅ Générateur de factures initialisé")
    
    def _charger_info_entreprise(self) -> Dict[str, Any]:
        """Charge les informations de l'entreprise depuis un fichier de config."""
        config_path = Path(self.template_dir) / "entreprise_config.json"
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"⚠️ Erreur chargement config entreprise: {e}")
        
        # Configuration par défaut
        config_defaut = {
            "nom": "Mon Entreprise",
            "adresse": "123 Rue de l'Exemple\n75000 Paris",
            "telephone": "01 23 45 67 89",
            "email": "<EMAIL>",
            "siret": "123 456 789 00012",
            "tva": "FR12345678901",
            "logo_path": None
        }
        
        # Sauvegarder la config par défaut
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_defaut, f, indent=2, ensure_ascii=False)
        
        return config_defaut
    
    def _creer_styles_personnalises(self):
        """Crée des styles personnalisés pour le PDF."""
        self.style_titre = ParagraphStyle(
            'TitreFacture',
            parent=self.styles['Heading1'],
            fontSize=24,
            textColor=colors.darkblue,
            alignment=TA_CENTER,
            spaceAfter=30
        )
        
        self.style_entreprise = ParagraphStyle(
            'Entreprise',
            parent=self.styles['Normal'],
            fontSize=12,
            textColor=colors.black,
            alignment=TA_LEFT
        )
        
        self.style_client = ParagraphStyle(
            'Client',
            parent=self.styles['Normal'],
            fontSize=11,
            textColor=colors.black,
            alignment=TA_LEFT
        )
    
    def generer_facture(self, 
                       donnees_facture: Dict[str, Any],
                       nom_fichier: Optional[str] = None) -> str:
        """
        Génère une facture PDF.
        
        Args:
            donnees_facture: Données de la facture
            nom_fichier: Nom du fichier de sortie
            
        Returns:
            Chemin vers le fichier PDF généré
        """
        try:
            # Générer le nom de fichier si non fourni
            if nom_fichier is None:
                numero_facture = donnees_facture.get('numero', f"F{datetime.now().strftime('%Y%m%d%H%M%S')}")
                nom_fichier = f"facture_{numero_facture}.pdf"
            
            # Créer le répertoire de sortie
            output_dir = Path("data/factures")
            output_dir.mkdir(exist_ok=True)
            
            fichier_path = output_dir / nom_fichier
            
            # Créer le document PDF
            doc = SimpleDocTemplate(
                str(fichier_path),
                pagesize=A4,
                rightMargin=2*cm,
                leftMargin=2*cm,
                topMargin=2*cm,
                bottomMargin=2*cm
            )
            
            # Construire le contenu
            story = []
            
            # En-tête avec informations entreprise
            story.extend(self._creer_entete())
            
            # Titre
            story.append(Paragraph("FACTURE", self.style_titre))
            story.append(Spacer(1, 20))
            
            # Informations facture et client
            story.extend(self._creer_info_facture(donnees_facture))
            story.append(Spacer(1, 30))
            
            # Tableau des articles
            story.extend(self._creer_tableau_articles(donnees_facture.get('articles', [])))
            story.append(Spacer(1, 20))
            
            # Totaux
            story.extend(self._creer_totaux(donnees_facture))
            story.append(Spacer(1, 30))
            
            # Conditions de paiement
            story.extend(self._creer_conditions_paiement(donnees_facture))
            
            # Générer le PDF
            doc.build(story)
            
            logger.info(f"✅ Facture générée: {fichier_path}")
            return str(fichier_path)
            
        except Exception as e:
            logger.error(f"❌ Erreur génération facture: {e}")
            raise
    
    def _creer_entete(self) -> List:
        """Crée l'en-tête avec les informations de l'entreprise."""
        elements = []
        
        # Tableau pour l'en-tête (logo + infos entreprise)
        data = [[
            Paragraph(f"<b>{self.entreprise_info['nom']}</b><br/>{self.entreprise_info['adresse']}", self.style_entreprise),
            Paragraph(f"Tél: {self.entreprise_info['telephone']}<br/>Email: {self.entreprise_info['email']}<br/>SIRET: {self.entreprise_info['siret']}", self.style_entreprise)
        ]]
        
        table = Table(data, colWidths=[10*cm, 7*cm])
        table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'RIGHT'),
        ]))
        
        elements.append(table)
        elements.append(Spacer(1, 30))
        
        return elements
    
    def _creer_info_facture(self, donnees: Dict[str, Any]) -> List:
        """Crée la section avec les informations de facture et client."""
        elements = []
        
        # Informations facture et client côte à côte
        info_facture = f"""
        <b>Facture N°:</b> {donnees.get('numero', 'N/A')}<br/>
        <b>Date:</b> {donnees.get('date', datetime.now().strftime('%d/%m/%Y'))}<br/>
        <b>Échéance:</b> {donnees.get('echeance', (datetime.now() + timedelta(days=30)).strftime('%d/%m/%Y'))}
        """
        
        client_info = donnees.get('client', {})
        info_client = f"""
        <b>Facturé à:</b><br/>
        {client_info.get('nom', 'Client')}<br/>
        {client_info.get('adresse', '')}<br/>
        {client_info.get('email', '')}
        """
        
        data = [[
            Paragraph(info_facture, self.style_client),
            Paragraph(info_client, self.style_client)
        ]]
        
        table = Table(data, colWidths=[8.5*cm, 8.5*cm])
        table.setStyle(TableStyle([
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
            ('ALIGN', (0, 0), (0, 0), 'LEFT'),
            ('ALIGN', (1, 0), (1, 0), 'LEFT'),
        ]))
        
        elements.append(table)
        return elements
    
    def _creer_tableau_articles(self, articles: List[Dict[str, Any]]) -> List:
        """Crée le tableau des articles/services."""
        elements = []
        
        # En-têtes du tableau
        headers = ['Description', 'Quantité', 'Prix unitaire', 'Total HT']
        data = [headers]
        
        # Ajouter les articles
        for article in articles:
            quantite = float(article.get('quantite', 1))
            prix_unitaire = float(article.get('prix_unitaire', 0))
            total = quantite * prix_unitaire
            
            data.append([
                article.get('description', ''),
                str(quantite),
                f"{prix_unitaire:.2f} €",
                f"{total:.2f} €"
            ])
        
        # Créer le tableau
        table = Table(data, colWidths=[8*cm, 2*cm, 3*cm, 4*cm])
        table.setStyle(TableStyle([
            # En-têtes
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            
            # Corps du tableau
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Description à gauche
            ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),  # Nombres à droite
            
            # Bordures
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        elements.append(table)
        return elements
    
    def _creer_totaux(self, donnees: Dict[str, Any]) -> List:
        """Crée la section des totaux."""
        elements = []
        
        # Calculer les totaux
        articles = donnees.get('articles', [])
        total_ht = sum(float(a.get('quantite', 1)) * float(a.get('prix_unitaire', 0)) for a in articles)
        taux_tva = float(donnees.get('taux_tva', 20))
        montant_tva = total_ht * (taux_tva / 100)
        total_ttc = total_ht + montant_tva
        
        # Tableau des totaux (aligné à droite)
        data = [
            ['', '', 'Total HT:', f"{total_ht:.2f} €"],
            ['', '', f'TVA ({taux_tva}%):', f"{montant_tva:.2f} €"],
            ['', '', 'Total TTC:', f"{total_ttc:.2f} €"]
        ]
        
        table = Table(data, colWidths=[8*cm, 2*cm, 3*cm, 4*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (2, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (2, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (2, 0), (-1, -1), 11),
            ('LINEBELOW', (2, -1), (-1, -1), 2, colors.black),
        ]))
        
        elements.append(table)
        return elements
    
    def _creer_conditions_paiement(self, donnees: Dict[str, Any]) -> List:
        """Crée la section des conditions de paiement."""
        elements = []
        
        conditions = donnees.get('conditions_paiement', 
                               "Paiement à 30 jours. Pénalités de retard : 3 fois le taux légal.")
        
        elements.append(Spacer(1, 20))
        elements.append(Paragraph("<b>Conditions de paiement:</b>", self.styles['Normal']))
        elements.append(Paragraph(conditions, self.styles['Normal']))
        
        return elements

def generer_facture_depuis_email(analyse_email: Dict[str, Any],
                                analyse_pieces_jointes: List[Dict[str, Any]]) -> Optional[str]:
    """
    Génère une facture basée sur l'analyse d'un email et de ses pièces jointes.

    Args:
        analyse_email: Analyse de l'email
        analyse_pieces_jointes: Analyses des pièces jointes

    Returns:
        Chemin vers la facture générée ou None
    """
    try:
        generateur = GenerateurFactures()

        # Extraire les informations du client depuis l'email
        expediteur = analyse_email.get('expediteur', '')
        client_info = {
            'nom': expediteur.split('<')[0].strip() if '<' in expediteur else expediteur,
            'email': expediteur.split('<')[1].replace('>', '') if '<' in expediteur else expediteur,
            'adresse': ''
        }

        # NOUVELLE LOGIQUE: Extraction intelligente avec validation
        contenu = analyse_email.get('contenu', '')

        # Pré-validation: vérifier que c'est bien une demande de facturation
        if not _est_demande_facturation_explicite(contenu):
            logger.warning("⚠️ Email ne semble pas être une demande de facturation explicite")
            return None

        # Extraction intelligente des services
        articles = _extraire_services_depuis_contenu(contenu)

        # Validation post-extraction
        if not articles:
            logger.warning("⚠️ Aucun service valide extrait de l'email")
            return None

        # Validation de cohérence globale
        total_estime = sum(a['quantite'] * a['prix_unitaire'] for a in articles)
        if total_estime <= 0:
            logger.warning("⚠️ Total estimé invalide")
            return None

        logger.info(f"✅ {len(articles)} services extraits, total estimé: {total_estime:.2f}€")

        # Analyser les pièces jointes pour extraire des articles/services supplémentaires
        for analyse in analyse_pieces_jointes:
            if analyse.get('est_commande') or analyse.get('montants_detectes'):
                # Créer un article basé sur l'analyse
                montants = analyse.get('montants_detectes', [])
                if montants:
                    articles.append({
                        'description': f"Service basé sur {analyse['nom']}",
                        'quantite': 1,
                        'prix_unitaire': max(montants)  # Prendre le montant le plus élevé
                    })

        # Article par défaut si rien n'est détecté
        if not articles:
            articles = [{
                'description': 'Prestation de service',
                'quantite': 1,
                'prix_unitaire': 100.0
            }]
        
        # Données de la facture
        donnees_facture = {
            'numero': f"F{datetime.now().strftime('%Y%m%d%H%M%S')}",
            'date': datetime.now().strftime('%d/%m/%Y'),
            'client': client_info,
            'articles': articles,
            'taux_tva': 20,
            'conditions_paiement': 'Paiement à 30 jours par virement bancaire.'
        }
        
        # Générer la facture
        fichier_facture = generateur.generer_facture(donnees_facture)
        logger.info(f"✅ Facture générée automatiquement: {fichier_facture}")
        
        return fichier_facture
        
    except Exception as e:
        logger.error(f"❌ Erreur génération facture depuis email: {e}")
        return None

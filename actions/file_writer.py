# actions/file_writer.py
# Module d'écriture de fichiers pour l'agent

import os
import json
import logging
import datetime
from typing import Dict, Any, Optional
from pathlib import Path

# Configuration du logging
logger = logging.getLogger(__name__)

def enregistrer_resultat(resultat: Dict[str, Any],
                        fichier_path: Optional[str] = None,
                        format_fichier: str = "json") -> bool:
    """
    Enregistre un résultat dans un fichier.

    Args:
        resultat: Résultat à enregistrer
        fichier_path: Chemin du fichier (par défaut: data/resultats/resultat_TIMESTAMP.json)
        format_fichier: Format du fichier (json, txt)

    Returns:
        True si l'enregistrement a réussi, False sinon
    """
    try:
        # Générer un nom de fichier par défaut si non fourni
        if fichier_path is None:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            os.makedirs("data/resultats", exist_ok=True)
            fichier_path = f"data/resultats/resultat_{timestamp}.{format_fichier}"

        # Créer le répertoire parent si nécessaire
        Path(fichier_path).parent.mkdir(parents=True, exist_ok=True)

        # Enregistrer selon le format
        if format_fichier.lower() == "json":
            with open(fichier_path, 'w', encoding='utf-8') as f:
                json.dump(resultat, f, indent=2, ensure_ascii=False, default=str)
        else:  # format texte
            with open(fichier_path, 'w', encoding='utf-8') as f:
                f.write(str(resultat))

        logger.info(f"✅ Résultat enregistré dans: {fichier_path}")
        return True

    except Exception as e:
        logger.error(f"❌ Erreur lors de l'enregistrement du résultat: {e}")
        return False
# Agent7.py - Cerveau principal de l'agent administratif intelligent
# Version optimisée GPT-4.1, architecture modulaire & robuste

import os
import logging
import json
import datetime
import sqlite3
import fcntl
import yaml
from typing import List, Any, Dict, Optional, Union, Tuple
from pathlib import Path
from dotenv import load_dotenv

# Ajout d'un module de configuration externe
def charger_configuration(config_path: str = "config/agent_config.yaml") -> Dict[str, Any]:
    """Charge la configuration depuis un fichier YAML externe."""
    try:
        # Créer le répertoire de configuration s'il n'existe pas
        os.makedirs(os.path.dirname(config_path), exist_ok=True)
        
        # Si le fichier n'existe pas, créer une configuration par défaut
        if not os.path.exists(config_path):
            config_defaut = {
                "memoire": {
                    "max_tokens": 1000,
                    "historique_fichier": "data/historique_conversations.json",
                    "utiliser_sqlite": True,
                    "sqlite_db": "data/historique.db"
                },
                "llm": {
                    "model_name": "gpt-4o",
                    "temperature": 0.2
                },
                "logging": {
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                }
            }
            os.makedirs(os.path.dirname(config_path), exist_ok=True)
            with open(config_path, 'w') as f:
                yaml.dump(config_defaut, f, default_flow_style=False)
            return config_defaut
        
        # Charger la configuration existante
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    except Exception as e:
        logger.error(f"❌ Erreur lors du chargement de la configuration: {e}")
        # Retourner une configuration par défaut en cas d'erreur
        return {
            "memoire": {
                "max_tokens": 1000,
                "historique_fichier": "data/historique_conversations.json",
                "utiliser_sqlite": False,
                "sqlite_db": "data/historique.db"
            },
            "llm": {
                "model_name": "gpt-4o",
                "temperature": 0.2
            }
        }

# Chargement de la configuration
CONFIG = charger_configuration()

# Modules internes
from config import OPENAI_API_KEY, LANGCHAIN_API_KEY, TENSORLAKE_API_KEY
from ingestion.document_ingestion import lire_documents
from memory.vector_store import stocker_vecteurs
from memory.memory_manager import gerer_memorisation
from reasoning.logic_analyzer import analyser_logique
from actions.notifier import notifier_action
from actions.file_writer import enregistrer_resultat
from schemas.contract_schema import ContractSchema
from datasets.dataset_manager import charger_donnees
from integrations.email_responder import lancer_traitement_emails_automatique

from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain_core.runnables import RunnableSequence
from langchain_core.tools import tool
from langchain.agents import AgentExecutor, create_react_agent
from langchain.tools import Tool
from langsmith import traceable
from langchain.memory import ConversationBufferMemory, ConversationSummaryMemory
from langchain_core.messages import HumanMessage, AIMessage

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Chargement des variables d'environnement avec vérification
load_dotenv()
required_env = ["OPENAI_API_KEY", "LANGCHAIN_API_KEY", "TENSORLAKE_API_KEY"]
for key in required_env:
    if not os.getenv(key):
        logger.error(f"❌ La variable d'environnement {key} est manquante.")
        raise EnvironmentError(f"Variable d'environnement {key} non définie.")

# Configuration du modèle LLM
def get_llm(model_name: str = "gpt-4o", temperature: float = 0.2) -> ChatOpenAI:
    """Crée et retourne une instance du modèle LLM avec les paramètres spécifiés."""
    try:
        return ChatOpenAI(
            model_name=model_name, 
            temperature=temperature
        )
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'initialisation du LLM: {e}")
        raise

# Initialisation du LLM
llm = get_llm()

# Définition du prompt pour l'analyse d'emails
email_prompt = PromptTemplate(
    input_variables=["email_content"],
    template="""
Tu es un assistant administratif intelligent. Analyse le contenu suivant d'un email :

"{email_content}"

Classifie ce message dans l'une des catégories suivantes :
1. Spam/Publicité
2. Important (demande de client, réunion, documents à traiter)
3. À traiter plus tard

Propose une action adaptée :
- Supprimer
- Répondre maintenant
- Marquer comme important
- Mettre en attente

Réponds sous la forme :
Catégorie : ...
Action suggérée : ...
Justification : ...
"""
)
email_chain = email_prompt | llm

@traceable
def analyser_email(email_content: str) -> str:
    """Analyse et classe un email, propose une action adaptée."""
    try:
        result = email_chain.invoke({"email_content": email_content})
        return getattr(result, "content", str(result))
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'analyse de l'email: {e}")
        raise

# Définition d'outils pour l'agent ReAct
@tool
def rechercher_document(query: str) -> str:
    """Recherche un document dans la base de connaissances."""
    try:
        # Implémentation à compléter avec votre logique de recherche
        logger.info(f"Recherche de documents pour: {query}")
        return f"Résultats pour '{query}': 3 documents trouvés."
    except Exception as e:
        logger.error(f"❌ Erreur lors de la recherche de documents: {e}")
        raise

@tool
def analyser_contrat(contract_id: str) -> Dict[str, Any]:
    """Analyse un contrat et extrait les informations clés."""
    try:
        # Implémentation à compléter avec votre logique d'analyse de contrats
        logger.info(f"Analyse du contrat: {contract_id}")
        return {
            "id": contract_id,
            "date_echeance": "15/12/2023",
            "montant": 5000,
            "statut": "actif"
        }
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'analyse du contrat: {e}")
        raise

@tool
def traiter_emails_automatique() -> str:
    """Traite automatiquement les emails non lus et génère des réponses."""
    try:
        rapport = lancer_traitement_emails_automatique()
        if "erreur" in rapport:
            return f"❌ Erreur traitement emails: {rapport['erreur']}"

        return f"✅ Emails traités: {rapport.get('emails_traites', 0)}, Réponses envoyées: {rapport.get('reponses_envoyees', 0)}, Factures générées: {rapport.get('factures_generees', 0)}"
    except Exception as e:
        logger.error(f"❌ Erreur lors du traitement des emails: {e}")
        return f"❌ Erreur: {str(e)}"

# Ajout d'une classe pour gérer la mémoire conversationnelle
class MemoireConversationnelle:
    """Gère la mémoire des conversations pour l'agent avec stockage robuste."""
    
    def __init__(self, max_tokens: Optional[int] = None):
        self.memoire_court_terme = ConversationBufferMemory(return_messages=True)
        self.memoire_long_terme = ConversationSummaryMemory(llm=get_llm(), return_messages=True)
        
        # Utiliser la configuration externe
        self.max_tokens = max_tokens or CONFIG["memoire"]["max_tokens"]
        self.historique_fichier = CONFIG["memoire"]["historique_fichier"]
        self.utiliser_sqlite = CONFIG["memoire"]["utiliser_sqlite"]
        self.sqlite_db = CONFIG["memoire"]["sqlite_db"]
        
        # Initialiser la base de données SQLite si nécessaire
        if self.utiliser_sqlite:
            self._initialiser_sqlite()
    
    def _initialiser_sqlite(self):
        """Initialise la base de données SQLite pour le stockage des conversations."""
        try:
            # Créer le répertoire si nécessaire
            os.makedirs(os.path.dirname(self.sqlite_db), exist_ok=True)
            
            # Créer la table si elle n'existe pas
            conn = sqlite3.connect(self.sqlite_db)
            cursor = conn.cursor()
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS conversations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT,
                humain TEXT,
                agent TEXT,
                tags TEXT
            )
            ''')
            conn.commit()
            conn.close()
            logger.info(f"✅ Base de données SQLite initialisée: {self.sqlite_db}")
        except Exception as e:
            logger.error(f"❌ Erreur lors de l'initialisation de SQLite: {e}")
            self.utiliser_sqlite = False
    
    def ajouter_interaction(self, humain: str, agent: str, tags: List[str] = None):
        """
        Ajoute une interaction à la mémoire court et long terme.
        
        Args:
            humain: Message de l'utilisateur
            agent: Réponse de l'agent
            tags: Liste optionnelle de tags pour catégoriser l'interaction
        """
        self.memoire_court_terme.chat_memory.add_user_message(humain)
        self.memoire_court_terme.chat_memory.add_ai_message(agent)
        
        # Ajouter à la mémoire long terme si nécessaire
        if len(self.memoire_court_terme.chat_memory.messages) > 5:
            self.memoire_long_terme.save_context(
                {"input": humain},
                {"output": agent}
            )
        
        # Sauvegarder dans le stockage persistant
        if self.utiliser_sqlite:
            self._sauvegarder_sqlite(humain, agent, tags)
        else:
            self._sauvegarder_json(humain, agent, tags)
    
    def _sauvegarder_json(self, humain: str, agent: str, tags: List[str] = None):
        """Sauvegarde l'interaction dans un fichier JSON avec verrouillage."""
        try:
            # Créer le répertoire si nécessaire
            os.makedirs(os.path.dirname(self.historique_fichier), exist_ok=True)
            
            # Charger l'historique existant avec verrouillage de fichier
            historique = []
            if os.path.exists(self.historique_fichier):
                with open(self.historique_fichier, 'r+') as f:
                    # Verrouiller le fichier pendant la lecture/écriture
                    fcntl.flock(f, fcntl.LOCK_EX)
                    try:
                        historique = json.load(f)
                    except json.JSONDecodeError:
                        historique = []
                    
                    # Ajouter la nouvelle interaction
                    historique.append({
                        "timestamp": datetime.datetime.now().isoformat(),
                        "humain": humain,
                        "agent": agent,
                        "tags": tags or []
                    })
                    
                    # Réécrire le fichier complet
                    f.seek(0)
                    f.truncate()
                    json.dump(historique, f, indent=2, ensure_ascii=False)
                    
                    # Libérer le verrou
                    fcntl.flock(f, fcntl.LOCK_UN)
            else:
                # Créer un nouveau fichier
                with open(self.historique_fichier, 'w') as f:
                    fcntl.flock(f, fcntl.LOCK_EX)
                    json.dump([{
                        "timestamp": datetime.datetime.now().isoformat(),
                        "humain": humain,
                        "agent": agent,
                        "tags": tags or []
                    }], f, indent=2, ensure_ascii=False)
                    fcntl.flock(f, fcntl.LOCK_UN)
                    
        except Exception as e:
            logger.error(f"❌ Erreur lors de la sauvegarde de l'historique JSON: {e}")
    
    def _sauvegarder_sqlite(self, humain: str, agent: str, tags: List[str] = None):
        """Sauvegarde l'interaction dans la base SQLite."""
        try:
            conn = sqlite3.connect(self.sqlite_db)
            cursor = conn.cursor()
            cursor.execute(
                "INSERT INTO conversations (timestamp, humain, agent, tags) VALUES (?, ?, ?, ?)",
                (
                    datetime.datetime.now().isoformat(),
                    humain,
                    agent,
                    json.dumps(tags or [])
                )
            )
            conn.commit()
            conn.close()
        except Exception as e:
            logger.error(f"❌ Erreur lors de la sauvegarde dans SQLite: {e}")
            # Fallback sur JSON en cas d'erreur
            self._sauvegarder_json(humain, agent, tags)
    
    def obtenir_historique(self, 
                          court_terme: bool = True, 
                          filtre_date: Optional[Tuple[str, str]] = None,
                          filtre_tags: Optional[List[str]] = None,
                          limite: int = 100) -> List[Dict[str, Any]]:
        """
        Récupère l'historique des conversations avec filtrage.
        
        Args:
            court_terme: Si True, utilise la mémoire court terme, sinon la mémoire persistante
            filtre_date: Tuple optionnel (date_debut, date_fin) pour filtrer par période
            filtre_tags: Liste optionnelle de tags pour filtrer les conversations
            limite: Nombre maximum d'entrées à retourner
            
        Returns:
            Liste d'interactions filtrées
        """
        if court_terme:
            messages = self.memoire_court_terme.chat_memory.messages
            return [{"role": "humain" if isinstance(m, HumanMessage) else "agent",
                    "contenu": m.content} for m in messages]
        
        # Récupérer depuis le stockage persistant
        if self.utiliser_sqlite:
            return self._obtenir_historique_sqlite(filtre_date, filtre_tags, limite)
        else:
            return self._obtenir_historique_json(filtre_date, filtre_tags, limite)
    
    def _obtenir_historique_json(self, filtre_date, filtre_tags, limite):
        """Récupère l'historique depuis le fichier JSON avec filtrage."""
        try:
            if not os.path.exists(self.historique_fichier):
                return []
                
            with open(self.historique_fichier, 'r') as f:
                fcntl.flock(f, fcntl.LOCK_SH)
                historique = json.load(f)
                fcntl.flock(f, fcntl.LOCK_UN)
            
            # Appliquer les filtres
            if filtre_date:
                debut, fin = filtre_date
                historique = [h for h in historique if debut <= h["timestamp"] <= fin]
            
            if filtre_tags:
                historique = [h for h in historique if any(tag in h.get("tags", []) for tag in filtre_tags)]
            
            # Limiter le nombre de résultats
            return historique[-limite:]
        except Exception as e:
            logger.error(f"❌ Erreur lors de la récupération de l'historique JSON: {e}")
            return []
    
    def _obtenir_historique_sqlite(self, filtre_date, filtre_tags, limite):
        """Récupère l'historique depuis SQLite avec filtrage."""
        try:
            conn = sqlite3.connect(self.sqlite_db)
            cursor = conn.cursor()
            
            query = "SELECT timestamp, humain, agent, tags FROM conversations"
            conditions = []
            params = []
            
            if filtre_date:
                debut, fin = filtre_date
                conditions.append("timestamp BETWEEN ? AND ?")
                params.extend([debut, fin])
            
            if filtre_tags:
                # Recherche de tags dans le JSON (simplifié)
                tag_conditions = []
                for tag in filtre_tags:
                    tag_conditions.append("tags LIKE ?")
                    params.append(f"%{tag}%")
                conditions.append("(" + " OR ".join(tag_conditions) + ")")
            
            if conditions:
                query += " WHERE " + " AND ".join(conditions)
            
            query += f" ORDER BY timestamp DESC LIMIT {limite}"
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            conn.close()
            
            return [
                {
                    "timestamp": row[0],
                    "humain": row[1],
                    "agent": row[2],
                    "tags": json.loads(row[3])
                }
                for row in rows
            ]
        except Exception as e:
            logger.error(f"❌ Erreur lors de la récupération de l'historique SQLite: {e}")
            return []
    
    def rejouer_conversation(self, identifiants: List[int] = None, 
                           date_debut: str = None, date_fin: str = None) -> Dict[str, Any]:
        """
        Rejoue une séquence de conversations pour tests ou démonstration.
        
        Args:
            identifiants: Liste d'IDs de conversations à rejouer (SQLite uniquement)
            date_debut: Date de début pour la sélection des conversations
            date_fin: Date de fin pour la sélection des conversations
            
        Returns:
            Résultats du rejeu avec métriques
        """
        conversations = []
        
        # Récupérer les conversations à rejouer
        if self.utiliser_sqlite and identifiants:
            try:
                conn = sqlite3.connect(self.sqlite_db)
                cursor = conn.cursor()
                placeholders = ",".join(["?"] * len(identifiants))
                cursor.execute(
                    f"SELECT timestamp, humain, agent FROM conversations WHERE id IN ({placeholders}) ORDER BY timestamp",
                    identifiants
                )
                conversations = cursor.fetchall()
                conn.close()
            except Exception as e:
                logger.error(f"❌ Erreur lors de la récupération des conversations pour rejeu: {e}")
        else:
            # Utiliser le filtrage par date
            filtre_date = None
            if date_debut and date_fin:
                filtre_date = (date_debut, date_fin)
            historique = self.obtenir_historique(court_terme=False, filtre_date=filtre_date)
            conversations = [(h["timestamp"], h["humain"], h["agent"]) for h in historique]
        
        # Rejouer les conversations
        resultats = []
        for timestamp, humain, agent in conversations:
            try:
                # Exécuter à nouveau la requête avec l'agent actuel
                nouveau_resultat = executer_tache_agent(humain)
                
                # Comparer avec la réponse originale
                similitude = "Identique" if nouveau_resultat["output"] == agent else "Différent"
                
                resultats.append({
                    "timestamp_original": timestamp,
                    "requete": humain,
                    "reponse_originale": agent,
                    "nouvelle_reponse": nouveau_resultat["output"],
                    "similitude": similitude
                })
                
                logger.info(f"Conversation rejouée du {timestamp}: {similitude}")
            except Exception as e:
                logger.error(f"❌ Erreur lors du rejeu de la conversation: {e}")
        
        return {
            "nombre_conversations": len(conversations),
            "conversations_rejouees": len(resultats),
            "resultats": resultats
        }

# Configuration de l'agent ReAct
def configurer_agent_react(memoire: Optional[MemoireConversationnelle] = None) -> AgentExecutor:
    """Configure un agent ReAct avec les outils disponibles et la mémoire."""
    try:
        # Définition des outils
        tools = [
            rechercher_document,
            analyser_contrat,
            traiter_emails_automatique,
            Tool(
                name="analyser_email",
                func=analyser_email,
                description="Analyse et classe un email, propose une action adaptée."
            )
        ]
        
        # Création du prompt pour l'agent avec contexte de mémoire
        agent_prompt = """Tu es un assistant administratif intelligent avec des capacités avancées.

{memoire}

Utilise les outils à ta disposition pour:
1. Analyser des emails et générer des réponses automatiques
2. Traiter les emails non lus avec génération de factures
3. Rechercher des documents dans la base de connaissances
4. Analyser des contrats et extraire des informations clés

Capacités spéciales:
- Génération automatique de factures PDF
- Analyse de pièces jointes (PDF, images, Excel, Word)
- Réponses email contextuelles et intelligentes
- Détection automatique de spam

Réfléchis étape par étape pour résoudre les problèmes et utilise les outils appropriés.
"""
        
        # Initialiser la mémoire si non fournie
        if memoire is None:
            memoire = MemoireConversationnelle()
        
        # Création de l'agent
        agent = create_react_agent(llm, tools, agent_prompt)
        agent_executor = AgentExecutor(
            agent=agent, 
            tools=tools, 
            verbose=True,
            memory=memoire.memoire_court_terme
        )
        
        return agent_executor
    except Exception as e:
        logger.error(f"❌ Erreur lors de la configuration de l'agent: {e}")
        raise

# Fonction pour exécuter l'agent sur une tâche
def executer_tache_agent(tache: str, memoire: Optional[MemoireConversationnelle] = None) -> Dict[str, Any]:
    """Exécute une tâche administrative avec l'agent ReAct."""
    try:
        # Initialiser la mémoire si non fournie
        if memoire is None:
            memoire = MemoireConversationnelle()
            
        agent_executor = configurer_agent_react(memoire)
        resultat = agent_executor.invoke({"input": tache})
        
        # Enregistrer l'interaction dans la mémoire
        memoire.ajouter_interaction(tache, resultat["output"])
        
        logger.info(f"Tâche traitée: {tache[:50]}...")
        return resultat
    except Exception as e:
        logger.error(f"❌ Erreur lors de l'exécution de la tâche: {e}")
        raise

# Pipeline principal amélioré
def pipeline_agent(documents: Optional[List[Any]] = None, taches: Optional[List[str]] = None) -> None:
    """
    Pipeline principal de l'agent administratif intelligent.
    
    Args:
        documents: Liste optionnelle de documents à traiter
        taches: Liste optionnelle de tâches à exécuter
    """
    logger.info("=== Lancement de l'agent administratif intelligent ===")
    
    try:
        # Initialisation de la mémoire conversationnelle
        memoire_conv = MemoireConversationnelle()
        
        # Traitement des documents
        if documents is None:
            documents = lire_documents()
        
        vecteurs = stocker_vecteurs(documents)
        memoire = gerer_memorisation(vecteurs)
        
        # Traitement des tâches avec l'agent ReAct
        if taches:
            logger.info(f"Traitement de {len(taches)} tâches avec l'agent ReAct")
            for tache in taches:
                try:
                    resultat = executer_tache_agent(tache, memoire_conv)
                    enregistrer_resultat(resultat)
                    logger.info(f"✅ Tâche traitée avec succès: {tache[:30]}...")
                except Exception as e:
                    logger.error(f"❌ Erreur lors du traitement de la tâche: {e}")
        
        # Analyse logique et actions
        analyses = analyser_logique(memoire)
        for analyse in analyses:
            notifier_action(analyse)
            enregistrer_resultat(analyse)
        
        logger.info("✅ Pipeline terminé avec succès.")
    except Exception as e:
        logger.error(f"❌ Erreur dans le pipeline principal: {e}")
        raise

if __name__ == "__main__":
    try:
        # Exemple d'utilisation avec des tâches spécifiques
        taches_exemple = [
            "Traiter automatiquement tous les emails non lus",
            "Analyser l'email du client XYZ concernant la facturation",
            "Rechercher les contrats arrivant à échéance ce mois-ci",
            "Préparer un résumé des demandes clients de la semaine"
        ]
        pipeline_agent(taches=taches_exemple)
    except Exception as e:
        logger.exception(f"Erreur critique dans l'agent: {e}")
